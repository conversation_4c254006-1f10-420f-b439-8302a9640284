"""
Multi-strategy framework for trading strategies

This module provides the base infrastructure for implementing and managing
multiple trading strategies that can run in parallel and combine their
target portfolios.
"""

from .base import BaseStrategy, StrategyResult
from .strategy_manager import StrategyManager

# Import strategy implementations
from .stat_arb_carry_trade import StatArbCarryTradeStrategy
from .cross_sectional_momentum import CrossSectionalMomentumStrategy

__all__ = [
    'BaseStrategy',
    'StrategyResult',
    'StrategyManager',
    'StatArbCarryTradeStrategy',
    'CrossSectionalMomentumStrategy'
]
