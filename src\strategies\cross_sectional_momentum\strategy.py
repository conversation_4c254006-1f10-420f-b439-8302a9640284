"""
Cross-Sectional Momentum Strategy Implementation

This strategy implements momentum-based trading by:
1. Selecting universe of top 50 coins by market cap with volume/volatility filters
2. Calculating beta-adjusted momentum features using multiple EMA timeframes
3. Using cross-sectional ranking to select top 5 longs and shorts
4. Applying sigmoid weighting + volatility targeting + beta neutrality
"""

import logging
import math
import numpy as np
import asyncio
from typing import Dict, List, Optional, Any, Tuple
import yaml
from pathlib import Path

from strategies.base import BaseStrategy, StrategyPosition
from utils.data_validation import DataUnitValidator

logger = logging.getLogger(__name__)


class CrossSectionalMomentumStrategy(BaseStrategy):
    """
    Cross-Sectional Momentum Strategy

    Implements momentum-based trading using beta-adjusted features,
    cross-sectional ranking, and volatility-targeted position sizing.
    """
    
    def __init__(self, data_fetcher, data_analyzer, exchange, main_config: Dict[str, Any]):
        """
        Initialize the strategy with shared resources
        
        Args:
            data_fetcher: Shared data fetcher instance
            data_analyzer: Shared data analyzer instance
            exchange: Shared exchange interface
            main_config: Main configuration dictionary
        """
        # Load strategy-specific configuration
        strategy_config = self._load_strategy_config()
        
        # Initialize base strategy
        super().__init__(
            strategy_name="cross_sectional_momentum",
            config=strategy_config,
            data_fetcher=data_fetcher,
            data_analyzer=data_analyzer,
            exchange=exchange
        )
        
        # Store main config for shared parameters
        self.main_config = main_config
        
        # Extract frequently used parameters
        self.total_capital = main_config.get('total_capital_usd', 10000)
        self.exchange_name = main_config.get('exchange', 'bybit')
        
        # Initialize data validator
        self.validator = DataUnitValidator()
        
        self.logger.info(f"🏗️ Cross-Sectional Momentum strategy initialized")
        self.logger.info(f"   Total capital: ${self.total_capital:,.0f}")
        self.logger.info(f"   Exchange: {self.exchange_name}")
        self.logger.info(f"   Target positions per leg: {self.config.get('max_positions_per_leg', 5)}")
        self.logger.info(f"   Target volatility: {self.config.get('target_volatility', 0.30):.1%}")
    
    def _load_strategy_config(self) -> Dict[str, Any]:
        """Load strategy-specific configuration from YAML file"""
        try:
            # Get the directory of this strategy file
            strategy_dir = Path(__file__).parent
            config_path = strategy_dir / 'config.yaml'
            
            if not config_path.exists():
                raise FileNotFoundError(f"Strategy config file not found: {config_path}")
            
            with open(config_path, 'r') as f:
                config = yaml.safe_load(f)
            
            logger.info(f"📁 Loaded strategy config from {config_path}")
            return config
            
        except Exception as e:
            logger.error(f"❌ Failed to load strategy config: {e}")
            raise
    
    def _validate_config(self) -> None:
        """Validate strategy-specific configuration parameters"""
        required_params = [
            'top_market_cap_count',
            'min_daily_volume_usd', 
            'min_volatility_threshold',
            'max_positions_per_leg',
            'target_volatility',
            'ema_timeframes'
        ]
        
        for param in required_params:
            if param not in self.config:
                raise ValueError(f"Missing required config parameter: {param}")
        
        # Validate numeric parameters
        if self.config['top_market_cap_count'] <= 0:
            raise ValueError("top_market_cap_count must be positive")
        
        if self.config['min_daily_volume_usd'] <= 0:
            raise ValueError("min_daily_volume_usd must be positive")
        
        if not (0 < self.config['min_volatility_threshold'] < 1):
            raise ValueError("min_volatility_threshold must be between 0 and 1")
        
        if self.config['max_positions_per_leg'] <= 0:
            raise ValueError("max_positions_per_leg must be positive")
        
        if not (0 < self.config['target_volatility'] < 1):
            raise ValueError("target_volatility must be between 0 and 1")
        
        # Validate EMA timeframes
        ema_timeframes = self.config.get('ema_timeframes', [])
        if not ema_timeframes:
            raise ValueError("ema_timeframes cannot be empty")
        
        for timeframe in ema_timeframes:
            if not isinstance(timeframe, list) or len(timeframe) != 2:
                raise ValueError("Each EMA timeframe must be a list of 2 integers")
            if timeframe[0] >= timeframe[1]:
                raise ValueError("First EMA period must be less than second EMA period")
        
        self.logger.info("✅ Strategy configuration validated successfully")
    
    async def get_universe(self) -> List[str]:
        """
        Get the universe of symbols this strategy will consider

        Selection criteria:
        - Top 50 by market cap (using CoinGecko API data)
        - Perpetual futures only (USDT pairs)
        - $3M minimum daily volume (30-day average)
        - Volatility above 5% (to filter out stablecoins)
        - Exclude stablecoins and wrapped coins

        Returns:
            List of symbol strings (e.g., ['BTCUSDT', 'ETHUSDT'])
        """
        try:
            self.logger.info("🌍 Selecting universe for cross-sectional momentum strategy...")
            
            # Get all available symbols from exchange
            all_symbols = await self.data_fetcher.get_available_symbols()
            if not all_symbols:
                self.logger.warning("⚠️ No symbols available from exchange")
                return []
            
            self.logger.info(f"📊 Found {len(all_symbols)} total symbols from exchange")
            
            # Filter for perpetual futures only (USDT pairs)
            perp_symbols = [s for s in all_symbols if s.endswith('USDT')]
            self.logger.info(f"📊 Filtered to {len(perp_symbols)} USDT perpetual futures")
            
            # Get market cap data from CoinGecko and volume data from exchange for filtering
            # Note: Implementation should use CoinGecko API for accurate market cap rankings
            # For now, using volume as proxy until CoinGecko integration is implemented
            
            eligible_symbols = []
            
            # Process symbols in batches to avoid overwhelming the API
            batch_size = 20
            for i in range(0, len(perp_symbols), batch_size):
                batch = perp_symbols[i:i + batch_size]
                
                # Get volume and volatility data for this batch
                batch_data = await self._get_universe_filter_data(batch)
                eligible_symbols.extend(batch_data)
                
                # Small delay between batches
                await asyncio.sleep(0.1)
            
            if not eligible_symbols:
                self.logger.warning("⚠️ No symbols passed initial filtering")
                return []
            
            # Sort by volume (proxy for market cap) and take top N
            top_count = self.config.get('top_market_cap_count', 50)
            eligible_symbols.sort(key=lambda x: x.get('avg_volume_30d_usdt', 0), reverse=True)
            
            # Take top symbols and extract symbol names
            top_symbols = [s['symbol'] for s in eligible_symbols[:top_count]]
            
            self.logger.info(f"✅ Selected universe of {len(top_symbols)} symbols")
            self.logger.info(f"   Top 10 by volume: {top_symbols[:10]}")
            
            return top_symbols
            
        except Exception as e:
            self.logger.error(f"❌ Failed to get universe: {e}")
            raise
    
    async def _get_universe_filter_data(self, symbols: List[str]) -> List[Dict[str, Any]]:
        """Get volume and volatility data for universe filtering"""
        eligible_symbols = []
        
        for symbol in symbols:
            try:
                # Get 30-day volume data
                volume_data = await self.data_fetcher.get_volume_data(symbol, days=30)
                if not volume_data:
                    continue
                
                avg_volume_30d = sum(volume_data) / len(volume_data) if volume_data else 0
                
                # Check volume threshold
                min_volume = self.config.get('min_daily_volume_usd', 3000000)
                if avg_volume_30d < min_volume:
                    continue
                
                # Get volatility data
                volatility = await self.data_analyzer.calculate_weighted_volatility(symbol)
                
                # Check volatility threshold
                min_volatility = self.config.get('min_volatility_threshold', 0.05)
                if volatility < min_volatility:
                    continue
                
                # Check for stablecoins and wrapped coins
                if self._is_excluded_symbol(symbol):
                    continue
                
                # Check minimum historical data requirement
                min_days = self.config.get('min_historical_data_days', 150)
                ohlcv_data = await self.data_fetcher.get_cached_ohlcv(symbol, '1d', min_days + 1)
                if not ohlcv_data or len(ohlcv_data) < min_days:
                    continue
                
                eligible_symbols.append({
                    'symbol': symbol,
                    'avg_volume_30d_usdt': avg_volume_30d,
                    'weighted_volatility': volatility
                })
                
            except Exception as e:
                self.logger.debug(f"⚠️ Failed to get filter data for {symbol}: {e}")
                continue
        
        return eligible_symbols
    
    def _is_excluded_symbol(self, symbol: str) -> bool:
        """Check if symbol should be excluded (stablecoins, wrapped coins)"""
        # Common stablecoin patterns
        stablecoin_patterns = ['USDT', 'USDC', 'BUSD', 'DAI', 'TUSD', 'FDUSD']
        
        # Common wrapped coin patterns  
        wrapped_patterns = ['WBTC', 'WETH', 'WBNB']
        
        symbol_base = symbol.replace('USDT', '').replace('USDC', '')
        
        # Check stablecoins
        if self.config.get('exclude_stablecoins', True):
            for pattern in stablecoin_patterns:
                if pattern in symbol_base:
                    return True
        
        # Check wrapped coins
        if self.config.get('exclude_wrapped_coins', True):
            for pattern in wrapped_patterns:
                if pattern in symbol_base:
                    return True
        
        return False

    async def calculate_features(self, symbols: List[str]) -> List[Dict[str, Any]]:
        """
        Calculate strategy-specific features for the given symbols

        Features calculated:
        1. Rolling beta against BTC (60-day window)
        2. Beta-adjusted log prices
        3. Weighted volatility (0.3×60d + 0.5×30d + 0.2×10d)
        4. Momentum signals from multiple EMA timeframes
        5. Combined trend signal (equal weight combination)

        Args:
            symbols: List of symbols to calculate features for

        Returns:
            List of dictionaries containing symbol data with calculated features
        """
        try:
            self.logger.info(f"📊 Calculating momentum features for {len(symbols)} symbols...")

            enriched_symbols = []

            # Process symbols in batches to manage memory and API limits
            batch_size = 10
            for i in range(0, len(symbols), batch_size):
                batch = symbols[i:i + batch_size]

                # Calculate features for this batch
                batch_results = await self._calculate_features_batch(batch)
                enriched_symbols.extend(batch_results)

                # Small delay between batches
                await asyncio.sleep(0.1)

            # Filter out symbols that failed feature calculation
            valid_symbols = [s for s in enriched_symbols if s.get('trend_signal') is not None]

            self.logger.info(f"✅ Successfully calculated features for {len(valid_symbols)}/{len(symbols)} symbols")

            if self.config.get('log_feature_calculations', False):
                for symbol_data in valid_symbols[:5]:  # Log first 5 for debugging
                    self.logger.info(f"📊 {symbol_data['symbol']}: "
                                   f"beta={symbol_data.get('beta', 0):.3f}, "
                                   f"vol={symbol_data.get('weighted_volatility', 0):.3f}, "
                                   f"trend={symbol_data.get('trend_signal', 0):.4f}")

            return valid_symbols

        except Exception as e:
            self.logger.error(f"❌ Failed to calculate features: {e}")
            raise

    async def _calculate_features_batch(self, symbols: List[str]) -> List[Dict[str, Any]]:
        """Calculate features for a batch of symbols"""
        batch_results = []

        # Get BTC data once for beta calculations
        btc_symbol = self.config.get('market_index_symbol', 'BTCUSDT')
        beta_days = self.config.get('beta_calculation_days', 60)

        # Get BTC OHLCV data for beta calculation
        btc_ohlcv = await self.data_fetcher.get_cached_ohlcv(btc_symbol, '1d', beta_days + 130)  # Extra days for EMA calculation
        if not btc_ohlcv:
            self.logger.warning(f"⚠️ Could not get BTC data for beta calculation")
            return []

        btc_log_prices = self._calculate_log_prices(btc_ohlcv)
        btc_returns = self._calculate_returns_from_log_prices(btc_log_prices)

        for symbol in symbols:
            try:
                symbol_data = await self._calculate_symbol_features(symbol, btc_log_prices, btc_returns)
                if symbol_data:
                    batch_results.append(symbol_data)

            except Exception as e:
                self.logger.debug(f"⚠️ Failed to calculate features for {symbol}: {e}")
                continue

        return batch_results

    async def _calculate_symbol_features(self, symbol: str, btc_log_prices: List[float],
                                       btc_returns: List[float]) -> Optional[Dict[str, Any]]:
        """Calculate all features for a single symbol"""
        try:
            # Get OHLCV data (need extra days for EMA calculation)
            min_days = self.config.get('min_historical_data_days', 150)
            ohlcv_data = await self.data_fetcher.get_cached_ohlcv(symbol, '1d', min_days + 30)

            if not ohlcv_data or len(ohlcv_data) < min_days:
                return None

            # 1. Calculate beta against BTC
            beta = await self._calculate_rolling_beta(symbol, ohlcv_data, btc_returns)
            if beta is None:
                beta = self.config.get('default_beta', 1.0)

            # 2. Calculate log prices
            coin_log_prices = self._calculate_log_prices(ohlcv_data)

            # 3. Calculate beta-adjusted log prices
            beta_adjusted_log_prices = self._calculate_beta_adjusted_log_prices(
                coin_log_prices, btc_log_prices, beta
            )

            # 4. Calculate weighted volatility
            weighted_volatility = await self.data_analyzer.calculate_weighted_volatility(symbol)

            # 5. Calculate momentum signals from multiple EMA timeframes
            trend_signal = self._calculate_trend_signal(beta_adjusted_log_prices, weighted_volatility)

            if trend_signal is None:
                return None

            return {
                'symbol': symbol,
                'beta': beta,
                'weighted_volatility': weighted_volatility,
                'trend_signal': trend_signal,
                'beta_adjusted_log_prices': beta_adjusted_log_prices[-1] if beta_adjusted_log_prices else 0  # Latest value
            }

        except Exception as e:
            self.logger.debug(f"⚠️ Error calculating features for {symbol}: {e}")
            return None

    def _calculate_log_prices(self, ohlcv_data: List[List]) -> List[float]:
        """Calculate log prices from OHLCV data"""
        try:
            log_prices = []
            for candle in ohlcv_data:
                close_price = float(candle[4])  # Close price is at index 4
                if close_price > 0:
                    log_prices.append(math.log(close_price))
                else:
                    # Handle zero/negative prices
                    if log_prices:
                        log_prices.append(log_prices[-1])  # Use previous value
                    else:
                        log_prices.append(0)  # Default to 0 if first value

            return log_prices

        except Exception as e:
            self.logger.debug(f"⚠️ Error calculating log prices: {e}")
            return []

    def _calculate_returns_from_log_prices(self, log_prices: List[float]) -> List[float]:
        """Calculate returns from log prices"""
        if len(log_prices) < 2:
            return []

        returns = []
        for i in range(1, len(log_prices)):
            returns.append(log_prices[i] - log_prices[i-1])

        return returns

    async def _calculate_rolling_beta(self, symbol: str, ohlcv_data: List[List],
                                    btc_returns: List[float]) -> Optional[float]:
        """Calculate rolling beta for the symbol against BTC"""
        try:
            # Calculate symbol returns
            coin_log_prices = self._calculate_log_prices(ohlcv_data)
            coin_returns = self._calculate_returns_from_log_prices(coin_log_prices)

            if len(coin_returns) < self.config.get('beta_calculation_days', 60):
                return None

            # Align returns (take the minimum length)
            min_length = min(len(coin_returns), len(btc_returns))
            if min_length < self.config.get('beta_calculation_days', 60):
                return None

            # Take the last N returns for beta calculation
            beta_days = self.config.get('beta_calculation_days', 60)
            coin_returns_aligned = coin_returns[-beta_days:]
            btc_returns_aligned = btc_returns[-beta_days:]

            # Calculate beta using numpy
            coin_array = np.array(coin_returns_aligned)
            btc_array = np.array(btc_returns_aligned)

            # Calculate covariance and variance
            covariance = np.cov(coin_array, btc_array)[0, 1]
            btc_variance = np.var(btc_array, ddof=1)

            if btc_variance == 0 or np.isnan(btc_variance) or np.isnan(covariance):
                return self.config.get('default_beta', 1.0)

            beta = covariance / btc_variance

            # Sanity check for extreme values
            if np.isnan(beta) or np.isinf(beta) or abs(beta) > 10:
                return self.config.get('default_beta', 1.0)

            return float(beta)

        except Exception as e:
            self.logger.debug(f"⚠️ Error calculating beta for {symbol}: {e}")
            return None

    def _calculate_beta_adjusted_log_prices(self, coin_log_prices: List[float],
                                          btc_log_prices: List[float], beta: float) -> List[float]:
        """Calculate beta-adjusted log prices: coin_log_price - (beta * btc_log_price)"""
        try:
            # Align the arrays to the same length
            min_length = min(len(coin_log_prices), len(btc_log_prices))
            if min_length == 0:
                return []

            beta_adjusted = []
            for i in range(min_length):
                adjusted_price = coin_log_prices[i] - (beta * btc_log_prices[i])
                beta_adjusted.append(adjusted_price)

            return beta_adjusted

        except Exception as e:
            self.logger.debug(f"⚠️ Error calculating beta-adjusted log prices: {e}")
            return []

    def _calculate_trend_signal(self, beta_adjusted_log_prices: List[float],
                              weighted_volatility: float) -> Optional[float]:
        """
        Calculate combined trend signal from multiple EMA timeframes

        Formula for each timeframe: (short_ema - long_ema) / weighted_volatility
        Equal weight combination of all timeframes
        """
        try:
            if not beta_adjusted_log_prices or weighted_volatility <= 0:
                return None

            ema_timeframes = self.config.get('ema_timeframes', [])
            if not ema_timeframes:
                return None

            timeframe_signals = []

            for short_period, long_period in ema_timeframes:
                # Calculate EMAs for this timeframe
                short_ema = self._calculate_ema(beta_adjusted_log_prices, short_period)
                long_ema = self._calculate_ema(beta_adjusted_log_prices, long_period)

                if short_ema is not None and long_ema is not None:
                    # Calculate signal: (short_ema - long_ema) / weighted_volatility
                    signal = (short_ema - long_ema) / weighted_volatility
                    timeframe_signals.append(signal)

            if not timeframe_signals:
                return None

            # Equal weight combination
            combined_signal = sum(timeframe_signals) / len(timeframe_signals)

            return combined_signal

        except Exception as e:
            self.logger.debug(f"⚠️ Error calculating trend signal: {e}")
            return None

    def _calculate_ema(self, prices: List[float], period: int) -> Optional[float]:
        """Calculate Exponential Moving Average"""
        try:
            if len(prices) < period:
                return None

            # Calculate smoothing factor
            alpha = 2.0 / (period + 1)

            # Initialize EMA with simple moving average of first 'period' values
            sma = sum(prices[:period]) / period
            ema = sma

            # Calculate EMA for remaining values
            for i in range(period, len(prices)):
                ema = alpha * prices[i] + (1 - alpha) * ema

            return ema

        except Exception as e:
            self.logger.debug(f"⚠️ Error calculating EMA: {e}")
            return None

    async def select_positions(self, enriched_symbols: List[Dict[str, Any]]) -> Tuple[List[Dict], List[Dict]]:
        """
        Select long and short position candidates using cross-sectional ranking

        Selection process:
        - Sort symbols by trend signal (highest to lowest)
        - Select top 5 highest trend signals for long positions (regardless of absolute value)
        - Select top 5 lowest trend signals for short positions (bottom 5 in cross-sectional ranking)

        Key Feature: This allows short positions even when trend signals are non-negative,
        as long as they rank as the lowest trend signals in the cross-sectional universe.

        Examples:
        - All positive signals [0.8, 0.6, 0.4, 0.2, 0.1]: Longs=[0.8,0.6,0.4,0.2,0.1], Shorts=[lowest 5]
        - All negative signals [-0.1, -0.2, -0.4]: Longs=[highest 5], Shorts=[lowest 5]
        - Mixed signals: Standard long/short based on cross-sectional ranking

        Args:
            enriched_symbols: Symbols with calculated features

        Returns:
            Tuple of (long_candidates, short_candidates)
        """
        try:
            self.logger.info(f"🎯 Selecting positions from {len(enriched_symbols)} enriched symbols...")

            if not enriched_symbols:
                self.logger.warning("⚠️ No enriched symbols for position selection")
                return [], []

            # Filter symbols with valid trend signals
            valid_symbols = [s for s in enriched_symbols if s.get('trend_signal') is not None]

            if not valid_symbols:
                self.logger.warning("⚠️ No symbols with valid trend signals")
                return [], []

            # Sort by trend signal (highest to lowest for cross-sectional ranking)
            sorted_symbols = sorted(valid_symbols, key=lambda x: x['trend_signal'], reverse=True)

            max_positions = self.config.get('max_positions_per_leg', 5)

            # Cross-sectional selection: always select top and bottom ranked symbols
            # This allows shorts even when trend signals are non-negative (as long as they rank lowest)

            if len(sorted_symbols) < 2 * max_positions:
                # If we have fewer symbols than needed for both legs, split them
                mid_point = len(sorted_symbols) // 2
                long_candidates = sorted_symbols[:mid_point]
                short_candidates = sorted_symbols[mid_point:] if mid_point > 0 else []
            else:
                # Standard case: select top N for longs, bottom N for shorts
                long_candidates = sorted_symbols[:max_positions]
                short_candidates = sorted_symbols[-max_positions:]

            self.logger.info(f"✅ Selected {len(long_candidates)} long and {len(short_candidates)} short candidates")

            if self.config.get('log_position_selection_details', False):
                self.logger.info("📊 Long candidates:")
                for i, candidate in enumerate(long_candidates):
                    self.logger.info(f"   {i+1}. {candidate['symbol']}: trend={candidate['trend_signal']:.4f}")

                self.logger.info("📊 Short candidates:")
                for i, candidate in enumerate(short_candidates):
                    self.logger.info(f"   {i+1}. {candidate['symbol']}: trend={candidate['trend_signal']:.4f}")

            return long_candidates, short_candidates

        except Exception as e:
            self.logger.error(f"❌ Failed to select positions: {e}")
            raise

    async def size_positions(self, long_candidates: List[Dict],
                           short_candidates: List[Dict]) -> List[StrategyPosition]:
        """
        Calculate position sizes for selected candidates

        Sizing methodology:
        1. Apply linear decay weights to positions based on ranking
        2. Apply volatility targeting to each position
        3. Beta projection disabled (beta adjustment done in feature calculation)

        Linear Decay Weights:
        - Long weights: [0.15, 0.125, 0.1, 0.075, 0.05] (top to bottom ranked)
        - Short weights: [-0.15, -0.125, -0.1, -0.075, -0.05] (top to bottom ranked)

        Args:
            long_candidates: Selected long position candidates (sorted by rank)
            short_candidates: Selected short position candidates (sorted by rank)

        Returns:
            List of StrategyPosition objects with calculated sizes
        """
        try:
            self.logger.info(f"💰 Calculating position sizes for {len(long_candidates)} longs and {len(short_candidates)} shorts...")

            if not long_candidates and not short_candidates:
                self.logger.warning("⚠️ No candidates for position sizing")
                return []

            all_candidates = long_candidates + short_candidates
            target_positions = []

            # Step 1: Calculate linear decay weights
            linear_weights = self._calculate_linear_decay_weights(long_candidates, short_candidates)

            # Step 2: Apply volatility targeting
            vol_adjusted_weights = self._apply_volatility_targeting(all_candidates, linear_weights)

            # Step 3: Create StrategyPosition objects with contract compliance and price validation
            target_positions = await self._create_positions_with_validation(
                all_candidates, vol_adjusted_weights, linear_weights, long_candidates
            )

            # Beta projection is disabled by default since beta adjustment is done in feature calculation
            # Can be optionally enabled in config if additional portfolio-level neutrality is desired
            if self.config.get('enable_beta_projection', False):
                target_positions = await self._apply_beta_projection(target_positions)

            total_capital_allocated = sum(pos.size_usd for pos in target_positions)

            self.logger.info(f"✅ Position sizing complete:")
            self.logger.info(f"   Total positions: {len(target_positions)}")
            self.logger.info(f"   Total capital allocated: ${total_capital_allocated:,.0f}")
            self.logger.info(f"   Capital utilization: {total_capital_allocated/self.total_capital:.1%}")

            return target_positions

        except Exception as e:
            self.logger.error(f"❌ Failed to calculate position sizes: {e}")
            raise

    def _calculate_linear_decay_weights(self, long_candidates: List[Dict], short_candidates: List[Dict]) -> List[float]:
        """
        Apply linear decay weights based on position ranking

        Linear Decay Weights:
        - Long weights: [0.15, 0.125, 0.1, 0.075, 0.05] (top to bottom ranked, sums to 0.5)
        - Short weights: [-0.15, -0.125, -0.1, -0.075, -0.05] (top to bottom ranked, sums to -0.5)

        Args:
            long_candidates: Long position candidates (already sorted by rank)
            short_candidates: Short position candidates (already sorted by rank)

        Returns:
            List of weights for all candidates (longs + shorts)
        """
        try:
            # Predefined linear decay weights for 5 positions per leg
            long_weights = [0.15, 0.125, 0.1, 0.075, 0.05]
            short_weights = [-0.15, -0.125, -0.1, -0.075, -0.05]

            linear_weights = []

            # Apply long weights (top ranked gets highest weight)
            for i in range(len(long_candidates)):
                if i < len(long_weights):
                    linear_weights.append(long_weights[i])
                else:
                    # Fallback for extra positions (shouldn't happen with max_positions_per_leg=5)
                    linear_weights.append(0.01)

            # Apply short weights (top ranked gets most negative weight)
            for i in range(len(short_candidates)):
                if i < len(short_weights):
                    linear_weights.append(short_weights[i])
                else:
                    # Fallback for extra positions (shouldn't happen with max_positions_per_leg=5)
                    linear_weights.append(-0.01)

            self.logger.debug(f"📊 Linear decay weights applied: {len(long_candidates)} longs, {len(short_candidates)} shorts")
            self.logger.debug(f"   Long weights: {linear_weights[:len(long_candidates)]}")
            self.logger.debug(f"   Short weights: {linear_weights[len(long_candidates):]}")

            return linear_weights

        except Exception as e:
            self.logger.error(f"❌ Error calculating linear decay weights: {e}")
            # Fallback to equal weights
            num_longs = len(long_candidates)
            num_shorts = len(short_candidates)
            equal_long_weight = 0.5 / num_longs if num_longs > 0 else 0
            equal_short_weight = -0.5 / num_shorts if num_shorts > 0 else 0

            fallback_weights = [equal_long_weight] * num_longs + [equal_short_weight] * num_shorts
            return fallback_weights

    def _apply_volatility_targeting(self, candidates: List[Dict], base_weights: List[float]) -> List[float]:
        """Apply volatility targeting to adjust weights"""
        try:
            if len(candidates) != len(base_weights):
                raise ValueError("Candidates and weights length mismatch")

            target_volatility = self.config.get('target_volatility', 0.30)
            vol_adjusted_weights = []

            for i, candidate in enumerate(candidates):
                base_weight = base_weights[i]
                asset_volatility = candidate.get('weighted_volatility', self.config.get('default_volatility', 0.20))

                # Avoid division by zero
                if asset_volatility <= 0:
                    asset_volatility = self.config.get('default_volatility', 0.20)

                # Calculate leverage adjustment: target_vol / asset_vol
                leverage_adjustment = target_volatility / asset_volatility

                # Apply adjustment to base weight
                adjusted_weight = base_weight * leverage_adjustment
                vol_adjusted_weights.append(adjusted_weight)

            return vol_adjusted_weights

        except Exception as e:
            self.logger.error(f"❌ Error applying volatility targeting: {e}")
            return base_weights  # Fallback to base weights

    def _normalize_weights(self, weights: List[float]) -> List[float]:
        """Normalize weights to sum to 1"""
        try:
            if not weights:
                return []

            total_weight = sum(abs(w) for w in weights)

            if total_weight == 0:
                # Equal weights fallback
                equal_weight = 1.0 / len(weights)
                return [equal_weight] * len(weights)

            # Normalize weights
            normalized_weights = [w / total_weight for w in weights]

            return normalized_weights

        except Exception as e:
            self.logger.error(f"❌ Error normalizing weights: {e}")
            equal_weight = 1.0 / len(weights) if weights else 0
            return [equal_weight] * len(weights)

    async def _apply_beta_projection(self, positions: List[StrategyPosition]) -> List[StrategyPosition]:
        """Apply beta projection for portfolio beta neutrality"""
        try:
            if not self.config.get('enable_beta_projection', True):
                return positions

            self.logger.info("🎯 Applying beta projection for portfolio neutrality...")

            # Import beta optimizer
            from execution.beta_optimizer import BetaOptimizer

            # Create beta optimizer with strategy config
            beta_config = {
                'enable_beta_projection': True,
                'beta_neutrality_tolerance': self.config.get('beta_neutrality_tolerance', 0.05),
                'beta_optimization_max_weight_change': self.config.get('beta_optimization_max_weight_change', 0.20)
            }

            beta_optimizer = BetaOptimizer(beta_config)

            # Apply beta optimization
            optimized_positions = await beta_optimizer.optimize_for_beta_neutrality(
                positions, self.data_analyzer.beta_calculator
            )

            if optimized_positions:
                self.logger.info("✅ Beta projection applied successfully")
                return optimized_positions
            else:
                self.logger.warning("⚠️ Beta projection failed, using original positions")
                return positions

        except Exception as e:
            self.logger.warning(f"⚠️ Beta projection failed: {e}, using original positions")
            return positions

    async def _create_positions_with_validation(self, all_candidates: List[Dict], vol_adjusted_weights: List[float],
                                              linear_weights: List[float], long_candidates: List[Dict]) -> List[StrategyPosition]:
        """
        Create positions with contract compliance and price validation
        """
        try:
            # Import contract spec manager for validation
            from utils.contract_specs import contract_spec_manager

            target_positions = []

            for i, candidate in enumerate(all_candidates):
                weight = vol_adjusted_weights[i]
                side = 'long' if candidate in long_candidates else 'short'

                # Calculate position size in USD
                position_size_usd = abs(weight) * self.total_capital

                # Get current price for position sizing
                price = float(candidate.get('price', candidate.get('last_price', 0)))
                if price <= 0:
                    self.logger.warning(f"⚠️ Invalid price for {candidate['symbol']}: {price}")
                    continue

                # Contract compliance: Calculate position size with rounding
                size_native, actual_position_usd = contract_spec_manager.calculate_position_size_with_rounding(
                    candidate['symbol'], position_size_usd, price)

                # Validate position size calculations
                if not DataUnitValidator.validate_position_size(candidate['symbol'], actual_position_usd, size_native, price):
                    self.logger.error(f"❌ Position size validation failed for {candidate['symbol']}")
                    continue

                # Round price for validation
                rounded_price = contract_spec_manager.round_price(candidate['symbol'], price, round_up=False)

                # Validate contract specifications
                is_valid, error_msg = contract_spec_manager.validate_order_specs(
                    candidate['symbol'], size_native, rounded_price)

                if not is_valid:
                    self.logger.error(f"❌ Contract spec validation failed for {candidate['symbol']}: {error_msg}")
                    continue

                target_positions.append(StrategyPosition(
                    symbol=candidate['symbol'],
                    side=side,
                    size_usd=actual_position_usd,
                    size_native=size_native,
                    weight=abs(weight),  # Store absolute weight for position sizing
                    metadata={
                        'trend_signal': candidate['trend_signal'],
                        'beta': candidate.get('beta', 1.0),
                        'weighted_volatility': candidate.get('weighted_volatility', 0.2),
                        'linear_weight': linear_weights[i],
                        'vol_adjusted_weight': vol_adjusted_weights[i],
                        'position_rank': i + 1,  # Track ranking position
                        'strategy_source': 'cross_sectional_momentum'
                    }
                ))

            return target_positions

        except Exception as e:
            self.logger.error(f"❌ Failed to create positions with validation: {e}")
            return []

    def get_strategy_info(self) -> Dict[str, Any]:
        """Get detailed information about this strategy"""
        base_info = super().get_strategy_info()

        strategy_info = {
            **base_info,
            'description': 'Cross-Sectional Momentum Strategy',
            'strategy_type': 'momentum',
            'universe_selection': 'top_50_market_cap_with_volume_volatility_filters',
            'feature_engineering': 'beta_adjusted_ema_momentum_signals',
            'position_selection': 'cross_sectional_ranking_top_5_per_leg',
            'position_sizing': 'linear_decay_weighting_with_volatility_targeting',
            'risk_management': 'volatility_targeting_and_beta_neutrality',
            'exchange': self.exchange_name,
            'total_capital': self.total_capital,
            'key_parameters': {
                'top_market_cap_count': self.config.get('top_market_cap_count', 50),
                'max_positions_per_leg': self.config.get('max_positions_per_leg', 5),
                'target_volatility': self.config.get('target_volatility', 0.30),
                'min_volatility_threshold': self.config.get('min_volatility_threshold', 0.05),
                'beta_calculation_days': self.config.get('beta_calculation_days', 60),
                'ema_timeframes': self.config.get('ema_timeframes', []),
                'enable_beta_projection': self.config.get('enable_beta_projection', True),
                'rebalancing_frequency': self.config.get('rebalancing_frequency', 'daily')
            },
            'filters': {
                'min_daily_volume_usd': self.config.get('min_daily_volume_usd', 3000000),
                'exclude_stablecoins': self.config.get('exclude_stablecoins', True),
                'exclude_wrapped_coins': self.config.get('exclude_wrapped_coins', True),
                'min_historical_data_days': self.config.get('min_historical_data_days', 150)
            }
        }

        return strategy_info
