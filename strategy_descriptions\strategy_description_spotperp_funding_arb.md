Strategy Name: Spot-Perpetual Funding Arbitrage Strategy
Strategy Type: Market Neutral Arbitrage

# 1. STRATEGY OVERVIEW
- Core Principle: Exploit price differences between spot and perpetual futures markets while collecting funding rates. If perpetual futures funding rate is positive, long spot, short perp. If perpetual futures funding rate is negative, long perp, short spot.
- Expected Edge: Risk premia. Providing liquidity to leveraged perpetual futures traders in crypto markets, assuming spot/perp prices will converge slowly over time. 
- Risks: Spot/perp price divergence
- Time Horizon: Medium-term (hold until better opportunity or until funding rate sign flips into the opposite signage)
- Market Conditions: Performs best in volatile markets with high funding rates; effective in mainly bull markets, less effective in bear markets due to high spot borrow costs.

# 2. UNIVERSE SELECTION
Selection Criteria:
- Volume Threshold: Minimum 5-day average $1M daily volume for both spot and perp
- Volatility Filters: Minimum 5% annualized weighted volatility filter to filter out stablecoins, use weighted_vol = 0.3 * 60d annualized rolling volatility + 0.5 * 30d annualized rolling volatility + 0.2 * 10d annualized rolling volatility.
- Exclusions: Stablecoins, assets without both spot and perp markets
- Additional Filters: Minimum annualized funding rate of 20%
- Listing Age: Exclude coins listed within the last 60 days
- Data Availability: Minimum 60 days of historical data required

Implementation Details:
- Data Source: Exchange API for both spot and perpetual data
- Refresh Frequency: Daily universe refresh
- Fallback Mechanism:  If primary data unavailable, use previous day's universe

# 3. FEATURE ENGINEERING
Primary Features:
- Annualized Funding Rate: Annualized funding rate. Bybit, Binance, OKX uses 8-hour funding rates. Hyperliquid uses 1-hour funding rates. Make sure the annualization factor is correct for 365-day markets in crypto, and for each exchange.
- Adjusted annualized funding rate: Annualized funding rate minus trading cost adjustment (10.95% annualized). For new positions, apply trading cost adjustment with floor protection to prevent sign flips. For held positions, use raw annualized funding rates (no cost penalty).

Feature Validation:
- Data Quality Checks: Verify both spot and perp data available with sufficient depth
- Normalization: Convert all funding rates to annualized values

Feature Combination:
- No feature combination, no feature decay

# 4. POSITION SELECTION
Selection Process:
- Signal Threshold: Minimum expected return of 20% annualized
- Ranking System: Rank by adjusted annualized funding rate (primary ranking). If adjusted annualized funding rate is equal, use raw funding rates (secondary ranking). If raw funding rates are equal, use 5-day average volume as tie-breaker.
- Long/Short Classification: 
  * Long spot + Short perp when perps funding rates are positive
  * Long perp + Short spot when perps funding rates are negative

Selection Constraints:
- Minimum Positions: 0 arbitrage pairs
- Maximum Positions: 10 arbitrage pairs
- Balance Requirements: Each position pair must be perfectly hedged (equal USD value long and short)

Entry Conditions:
- Timing Rules: Rebalance daily at 23:00 UTC (1 hour before funding)
- Confirmation Signals: Verify sufficient liquidity in both spot and perp markets
- Entry Execution: Use existing execution framework.

# 5. POSITION SIZING
Sizing Methodology:
- Base Approach: Adjusted funding rate weighting. Use sigmoid function y = tanh(2x)
- Target Volatility: Inherently vol neutralized (paired positions)
- Position Limits: Maximum 20% of capital per arbitrage pair.

Risk Controls:
- Beta Neutrality: Inherently beta-neutral (paired positions)
- Sector Neutrality: Not applicable (each pair is self-hedged)
- Leverage Limits: 0.1x to 5.0x per position pair

Dynamic Adjustments:
- No additional adjustments needed.

# 6. EXIT RULES
Profit Taking:
- Target Levels: Exit when funding rate flips sign.
- Scaling Out: The sigmoid function automatically scales out of positions as funding rates decay.
- Time-Based Exits: No time based exits

Stop Losses:
- Signal Reversal: Exit if funding rate flips sign

Rebalancing:
- Schedule: Daily rebalancing at 23:00 UTC (1 hour before funding)
- Buffer Zones: Use existing buffer zone framework.

# 7. CONFIGURATION PARAMETERS
