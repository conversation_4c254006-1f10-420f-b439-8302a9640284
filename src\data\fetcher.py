"""
Data fetching with concurrent processing and caching
"""

import asyncio
import time
import logging
import statistics
from decimal import Decimal
from typing import Dict, List, Optional, Any

from exchanges.base import ExchangeInterface
from data.cache import DataCache, CacheKey
from utils.monitoring import PerformanceMonitor
from utils.data_validation import DataUnitValidator
from utils.contract_specs import contract_spec_manager
from utils.error_handler import ErrorRecoveryManager, ErrorClassifier

logger = logging.getLogger(__name__)


class DataFetcher:
    """High-performance data fetcher with concurrent processing and caching"""
    
    def __init__(self, exchange: ExchangeInterface, config: Dict[str, Any],
                 cache: Optional[DataCache] = None, monitor: Optional[PerformanceMonitor] = None):
        self.exchange = exchange
        self.config = config

        # Configure cache with settings from config
        if cache is None:
            cache_ttl = config.get('cache_default_ttl', 300)
            cache_size = config.get('cache_max_size', 1000)
            cache_gc_interval = config.get('cache_gc_interval', 300)
            cache = DataCache(default_ttl=cache_ttl, max_size=cache_size, gc_interval=cache_gc_interval)
            logger.debug(f"🔧 Configured cache: TTL={cache_ttl}s, max_size={cache_size}, GC_interval={cache_gc_interval}s")

        self.cache = cache
        self.monitor = monitor or PerformanceMonitor()

        # Rate limiting
        self.api_semaphore = asyncio.Semaphore(config.get('max_concurrent_api_calls', 10))
        self.rate_limit_delay = config.get('rate_limit_delay_ms', 100) / 1000

        # Enhanced error recovery manager
        self.error_manager = ErrorRecoveryManager(config)
    
    async def _rate_limited_api_call(self, coro):
        """Enhanced rate-limited API call wrapper with sophisticated error handling"""
        async def _execute_api_call():
            async with self.api_semaphore:
                await asyncio.sleep(self.rate_limit_delay)
                start_time = time.time()
                result = await coro
                duration = time.time() - start_time

                self.monitor.record_timing('api_call', duration)
                return result

        try:
            # Use enhanced error recovery manager
            return await self.error_manager.execute_with_retry(
                _execute_api_call,
                exchange=self.exchange.name,
                operation="api_call"
            )
        except Exception as e:
            # Classify and log the error
            classified_error = ErrorClassifier.classify_error(e, self.exchange.name)
            logger.warning(f"⚠️ API call failed after retries: {classified_error}")
            raise classified_error
    

    
    async def get_cached_ohlcv(self, symbol: str, timeframe: str, limit: int) -> Optional[List[List]]:
        """Get OHLCV data with caching"""
        cache_key = CacheKey.ohlcv(symbol, timeframe, limit)
        
        # Check cache first
        cached_data = self.cache.get(cache_key)
        if cached_data is not None:
            self.monitor.increment_counter('cache_hits')
            logger.debug(f"📋 Using cached OHLCV data for {symbol} {timeframe}")
            return cached_data
        
        # Cache miss - fetch fresh data
        self.monitor.increment_counter('cache_misses')
        try:
            ohlcv_data = await self._rate_limited_api_call(
                self.exchange.fetch_ohlcv(symbol, timeframe, limit=limit)
            )

            if ohlcv_data:
                # Clean data by removing NaN rows first
                cleaned_data = DataUnitValidator.clean_ohlcv_data(ohlcv_data, symbol)

                if not cleaned_data:
                    logger.error(f"❌ No valid OHLCV data remaining after cleaning for {symbol}")
                    return None

                # Validate cleaned OHLCV data format and units
                if DataUnitValidator.validate_ohlcv_data(cleaned_data, symbol):
                    self.cache.set(cache_key, cleaned_data)
                    logger.debug(f"📊 Fetched, cleaned, and cached OHLCV data for {symbol} {timeframe}")
                    return cleaned_data
                else:
                    logger.error(f"❌ OHLCV data validation failed for {symbol}")
                    return None

            return ohlcv_data
            
        except Exception as e:
            logger.error(f"❌ Error fetching OHLCV for {symbol}: {e}")
            return None
    
    async def calculate_usdt_volume(self, symbol: str, days: int = 5) -> Optional[float]:
        """Calculate average USDT volume over specified days

        CRITICAL: This function assumes OHLCV volume is in BASE currency (e.g., BTC for BTCUSDT)
        and converts it to USDT volume by multiplying by price. This is correct for:
        - Bybit USDT perpetuals: volume is in base currency (BTC)
        - Binance USDT perpetuals: volume is in base currency (BTC)
        """
        cache_key = CacheKey.volume(symbol, days)

        # Check cache first
        cached_volume = self.cache.get(cache_key)
        if cached_volume is not None:
            return cached_volume

        try:
            # Method 1: Try to get quote volume directly from ticker
            quote_volume_24h = await self._try_fetch_quote_volume(symbol)
            if quote_volume_24h is not None and quote_volume_24h > 0:
                logger.debug(f"📊 {symbol} using 24h quote volume from ticker: ${quote_volume_24h:,.0f}")
                self.cache.set(cache_key, quote_volume_24h)
                return quote_volume_24h

            # Method 2: Calculate from OHLCV data
            logger.debug(f"📊 {symbol} calculating USDT volume from OHLCV data...")
            ohlcv_data = await self.get_cached_ohlcv(symbol, '1d', days)

            if not ohlcv_data or len(ohlcv_data) < days:
                logger.debug(f"⚠️ Insufficient OHLCV data for {symbol} volume calculation")
                return None

            usdt_volumes = []
            exchange_name = self.exchange.name.lower()

            for candle in ohlcv_data:
                try:
                    # CCXT OHLCV format: [timestamp, open, high, low, close, volume]
                    volume_raw = float(candle[5])  # Volume field
                    close_price = float(candle[4])  # Close price

                    # Validate basic data
                    if volume_raw <= 0 or close_price <= 0:
                        continue

                    # Convert volume to USDT using exchange-specific logic
                    volume_usdt = self._convert_volume_to_usdt(exchange_name, symbol, volume_raw, close_price)

                    if volume_usdt and volume_usdt > 0:
                        # Validate the conversion
                        validated_volume = DataUnitValidator.convert_and_validate_volume(symbol, volume_usdt)
                        if validated_volume:
                            usdt_volumes.append(validated_volume)
                        else:
                            logger.debug(f"⚠️ Volume validation failed for {symbol}: {volume_usdt}")

                except (ValueError, IndexError, TypeError) as e:
                    logger.debug(f"⚠️ Error processing volume data for {symbol}: {e}")
                    continue

            if not usdt_volumes:
                logger.warning(f"⚠️ No valid OHLCV data for {symbol} volume calculation")
                return None

            # Calculate average USDT volume
            avg_usdt_volume = sum(usdt_volumes) / len(usdt_volumes)
            logger.debug(f"📊 {symbol} {days}-day average USDT volume: ${avg_usdt_volume:,.0f} "
                        f"(calculated from {len(usdt_volumes)} valid candles)")

            self.cache.set(cache_key, avg_usdt_volume)
            return avg_usdt_volume

        except Exception as e:
            logger.error(f"❌ Error calculating USDT volume for {symbol}: {e}")
            return None

    def _convert_volume_to_usdt(self, exchange_name: str, symbol: str, volume_raw: float,
                               close_price: float) -> Optional[float]:
        """Convert raw volume to USDT equivalent based on exchange-specific format

        Args:
            exchange_name: Name of the exchange (bybit, binance, etc.)
            symbol: Trading symbol
            volume_raw: Raw volume from OHLCV data
            close_price: Close price for conversion

        Returns:
            Volume in USDT equivalent or None if conversion fails
        """
        try:
            if exchange_name == 'bybit':
                # Bybit: volume is in base currency, need to convert to USDT
                # For BTCUSDT: volume is in BTC, multiply by price to get USDT
                volume_usdt = volume_raw * close_price
                logger.debug(f"📊 Bybit {symbol}: {volume_raw:.6f} base * ${close_price:.2f} = ${volume_usdt:.2f} USDT")
                return volume_usdt

            elif exchange_name in ['binance', 'binanceusdm']:
                # Binance: volume is base asset volume, need to convert to USDT
                # For BTCUSDT: volume is in BTC, multiply by price to get USDT
                volume_usdt = volume_raw * close_price
                logger.debug(f"📊 Binance {symbol}: {volume_raw:.6f} base * ${close_price:.2f} = ${volume_usdt:.2f} USDT")
                return volume_usdt

            elif exchange_name == 'okx':
                # OKX: volume is typically in base currency for perpetuals
                volume_usdt = volume_raw * close_price
                logger.debug(f"📊 OKX {symbol}: {volume_raw:.6f} base * ${close_price:.2f} = ${volume_usdt:.2f} USDT")
                return volume_usdt

            elif exchange_name == 'hyperliquid':
                # Hyperliquid: volume is in base currency
                volume_usdt = volume_raw * close_price
                logger.debug(f"📊 Hyperliquid {symbol}: {volume_raw:.6f} base * ${close_price:.2f} = ${volume_usdt:.2f} USDT")
                return volume_usdt

            else:
                # Default: assume volume is in base currency
                volume_usdt = volume_raw * close_price
                logger.debug(f"📊 {exchange_name} {symbol}: {volume_raw:.6f} base * ${close_price:.2f} = ${volume_usdt:.2f} USDT (default)")
                return volume_usdt

        except Exception as e:
            logger.error(f"❌ Error converting volume for {symbol} on {exchange_name}: {e}")
            return None

    async def _try_fetch_quote_volume(self, symbol: str) -> Optional[float]:
        """Try to fetch quote volume directly if supported by exchange"""
        try:
            ticker = await self.exchange.fetch_ticker(symbol)
            
            # Check for various quote volume fields
            quote_volume_fields = ['quoteVolume', 'quote_volume', 'baseVolume24h', 'quoteVolume24h']
            
            for field in quote_volume_fields:
                if field in ticker and ticker[field] is not None and ticker[field] > 0:
                    return ticker[field]
            
            # Calculate from base volume and price
            if ('baseVolume' in ticker and ticker['baseVolume'] is not None and
                'last' in ticker and ticker['last'] is not None):
                base_volume = ticker['baseVolume']
                price = ticker['last']
                return base_volume * price
            
            return None
            
        except Exception as e:
            logger.debug(f"⚠️ Could not fetch quote volume for {symbol}: {e}")
            return None

    async def get_eligible_coins(self) -> List[Dict[str, Any]]:
        """Get coins eligible for carry trade using concurrent data fetching"""
        try:
            # Fetch markets and tickers concurrently
            logger.info("📡 Fetching markets and tickers concurrently...")
            markets_task = self._rate_limited_api_call(self.exchange.fetch_markets())
            tickers_task = self._rate_limited_api_call(self.exchange.fetch_tickers())

            markets, tickers = await asyncio.gather(markets_task, tickers_task, return_exceptions=True)

            if isinstance(markets, Exception):
                logger.error(f"❌ Failed to fetch markets: {markets}")
                return []
            if isinstance(tickers, Exception):
                logger.error(f"❌ Failed to fetch tickers: {tickers}")
                return []

            logger.debug(f"✅ Successfully fetched {len(markets)} markets and {len(tickers)} tickers")

            # Cache market information for contract specifications
            contract_spec_manager.cache_market_info(markets)
            logger.debug(f"📋 Cached market info for contract specifications")

            # Filter USDT-margined perpetual futures only
            perp_markets = {symbol: market for symbol, market in markets.items()
                           if (market.get('type') == 'swap' and
                               market.get('active', True) and
                               market.get('settle') == 'USDT' and
                               ':USDT' in symbol)}

            logger.info(f"📈 Found {len(perp_markets)} USDT-margined perpetual futures contracts")

            # Process symbols in batches for better memory management
            batch_size = self.config.get('symbol_processing_batch_size', 50)
            symbols = list(perp_markets.keys())
            eligible_coins = []

            start_time = time.time()
            for i in range(0, len(symbols), batch_size):
                batch_symbols = symbols[i:i + batch_size]
                batch_num = i//batch_size + 1
                total_batches = (len(symbols) + batch_size - 1)//batch_size

                logger.info(f"📊 Processing batch {batch_num}/{total_batches} ({len(batch_symbols)} symbols)")

                # Process batch concurrently
                batch_start = time.time()
                batch_results = await self._process_symbol_batch(batch_symbols, perp_markets, tickers)
                batch_duration = time.time() - batch_start

                eligible_coins.extend(batch_results)

                # Log batch performance
                logger.info(f"✅ Batch {batch_num} completed in {batch_duration:.2f}s: {len(batch_results)} eligible coins found")

                # Small delay between batches to be respectful to the exchange
                if i + batch_size < len(symbols):
                    await asyncio.sleep(1)

            total_duration = time.time() - start_time

            # Log performance summary
            cache_stats = self.cache.get_stats()
            logger.info(f"📈 Processing completed in {total_duration:.2f}s")
            logger.info(f"📊 Performance: {cache_stats['hits']} cache hits, "
                       f"{cache_stats['hit_rate']} cache hit rate")

            logger.info(f"✅ Found {len(eligible_coins)} eligible coins after filtering")
            return eligible_coins

        except Exception as e:
            logger.error(f"❌ Critical error in get_eligible_coins: {e}")
            return []

    async def _process_symbol_batch(self, symbols: List[str], perp_markets: Dict, tickers: Dict) -> List[Dict]:
        """Process a batch of symbols concurrently"""
        tasks = []
        for symbol in symbols:
            task = self._process_single_symbol(symbol, perp_markets[symbol], tickers.get(symbol))
            tasks.append(task)

        # Process all symbols in the batch concurrently
        results = await asyncio.gather(*tasks, return_exceptions=True)

        # Filter out exceptions and None results
        eligible_coins = []
        for result in results:
            if isinstance(result, dict):  # Valid coin data
                eligible_coins.append(result)
            elif isinstance(result, Exception):
                logger.debug(f"⚠️ Symbol processing failed: {result}")

        return eligible_coins

    async def _process_single_symbol(self, symbol: str, market: Dict, ticker: Optional[Dict]) -> Optional[Dict]:
        """Process a single symbol to determine eligibility"""
        start_time = time.time()
        try:
            if not ticker:
                logger.debug(f"⏭️ Skipping {symbol} - no ticker data")
                return None

            # Dynamic new listing detection
            exclude_days = self.config.get('exclude_new_listings_days', 60)
            min_data_days = self.config.get('min_historical_data_days', 60)

            # Method 1: Check listing time from exchange metadata
            list_time = None
            info = market.get('info', {})

            # Check various possible fields for listing time
            for field in ['list_time', 'listTime', 'launchTime', 'launch_time', 'onboardDate']:
                if field in info and info[field]:
                    try:
                        list_time = int(info[field])
                        break
                    except (ValueError, TypeError):
                        continue

            # If we found a listing time, check if it's recent
            if list_time:
                # Convert to seconds if it looks like milliseconds
                if list_time > 1e12:  # Likely milliseconds
                    list_time = list_time / 1000

                cutoff_time = time.time() - (exclude_days * 86400)
                if list_time > cutoff_time:
                    days_since_listing = (time.time() - list_time) / 86400
                    logger.debug(f"⏭️ Skipping {symbol} - new listing detected via timestamp "
                               f"({days_since_listing:.1f} days old, need {exclude_days}+ days)")
                    return None

            # Method 2: Check historical data availability
            # If we can't get enough historical data, it's likely a new listing
            try:
                # Try to fetch OHLCV data for the minimum required period
                ohlcv_data = await self._rate_limited_api_call(
                    self.exchange.fetch_ohlcv(symbol, '1d', limit=min_data_days + 1)
                )

                if not ohlcv_data or len(ohlcv_data) < min_data_days:
                    available_days = len(ohlcv_data) if ohlcv_data else 0
                    logger.debug(f"⏭️ Skipping {symbol} - new listing detected via data availability "
                               f"({available_days} days available, need {min_data_days}+ days)")
                    return None

            except Exception as e:
                logger.debug(f"⏭️ Skipping {symbol} - failed to fetch historical data: {e}")
                return None

            # Fetch all required data concurrently for this symbol
            volume_task = self.calculate_usdt_volume(symbol, days=5)
            funding_rate_task = self._rate_limited_api_call(self.exchange.fetch_funding_rate(symbol))
            funding_history_task = self._rate_limited_api_call(self.exchange.fetch_funding_rate_history(symbol, limit=9))

            # Wait for all data to be fetched
            volume_result, funding_rate_result, funding_history_result = await asyncio.gather(
                volume_task, funding_rate_task, funding_history_task,
                return_exceptions=True
            )

            # Update performance stats
            processing_time = time.time() - start_time
            self.monitor.record_timing('symbol_processing', processing_time)

            # Check volume
            if isinstance(volume_result, Exception) or volume_result is None:
                logger.debug(f"⏭️ Skipping {symbol} - volume calculation failed")
                return None

            if volume_result < self.config['min_daily_volume_usd']:
                logger.debug(f"⏭️ Skipping {symbol} - low USDT volume")
                return None

            # Check funding rate
            if isinstance(funding_rate_result, Exception):
                logger.debug(f"⏭️ Skipping {symbol} - funding rate fetch failed")
                return None

            current_funding = Decimal(str(funding_rate_result.get('fundingRate', 0)))

            # Check funding history
            if isinstance(funding_history_result, Exception) or len(funding_history_result) < 3:
                logger.debug(f"⏭️ Skipping {symbol} - insufficient funding history")
                return None

            # Clean funding rate history by removing NaN entries
            cleaned_funding_history = DataUnitValidator.clean_funding_rate_history(funding_history_result, symbol)

            if len(cleaned_funding_history) < 3:
                logger.debug(f"⏭️ Skipping {symbol} - insufficient valid funding history after cleaning")
                return None

            # Calculate mean 3-day funding rate from cleaned data with validation
            funding_rates = []
            for entry in cleaned_funding_history:
                rate = entry.get('fundingRate', 0)
                validated_rate = DataUnitValidator.convert_and_validate_funding_rate(symbol, rate)
                if validated_rate is not None:
                    funding_rates.append(validated_rate)
                else:
                    if self.config.get('debug_mode', False):
                        logger.debug(f"⚠️ Invalid funding rate entry for {symbol}: {rate}")

            if not funding_rates:
                if self.config.get('debug_mode', False):
                    logger.debug(f"⏭️ Skipping {symbol} - no valid funding rates after validation")
                return None

            mean_funding_3d = Decimal(str(statistics.mean(funding_rates)))

            # Annualize funding rates based on exchange funding intervals
            exchange_name = self.exchange.name.lower()
            if exchange_name == 'hyperliquid':
                # Hyperliquid uses 1-hour funding rates
                annualization_factor = 365 * 24  # 8760
            else:
                # Bybit, Binance, OKX use 8-hour funding rates
                annualization_factor = 365 * 24 / 8  # 1095

            # Convert to annualized funding rates
            annualized_funding_3d = mean_funding_3d * annualization_factor

            # Apply trading cost adjustment (10.95% annualized)
            trading_cost_annualized = Decimal("0.1095")  # 10.95% annualized

            # Calculate adjusted funding rate with floor protection
            # For new positions, apply trading cost adjustment with floor protection to prevent sign flips
            if annualized_funding_3d > 0:
                # Positive funding rate - subtract cost (but don't let it go negative)
                adjusted_funding = max(Decimal("0"), annualized_funding_3d - trading_cost_annualized)
            else:
                # Negative funding rate - add cost (but don't let it go positive)
                adjusted_funding = min(Decimal("0"), annualized_funding_3d + trading_cost_annualized)

            # Validate price and volume data
            price_raw = ticker.get('last', 0)
            validated_price = DataUnitValidator.convert_and_validate_price(symbol, price_raw, min_price=0.0001)
            if validated_price is None:
                logger.debug(f"⏭️ Skipping {symbol} - invalid price: {price_raw}")
                return None

            validated_volume = DataUnitValidator.convert_and_validate_volume(symbol, volume_result, min_volume=0)
            if validated_volume is None:
                logger.debug(f"⏭️ Skipping {symbol} - invalid volume: {volume_result}")
                return None

            # Get current funding rate for reference
            current_funding = funding_rates[-1] if funding_rates else 0
            annualized_current_funding = current_funding * annualization_factor

            logger.debug(f"✅ Added eligible coin: {symbol} (annualized funding: {annualized_funding_3d:.4f}, adjusted: {adjusted_funding:.4f}, price: ${validated_price:.4f}, volume: ${validated_volume:,.0f})")

            return {
                'symbol': symbol,
                'mean_funding_3d': mean_funding_3d,  # Raw 3-day funding rate (not annualized)
                'annualized_funding_3d': annualized_funding_3d,  # Annualized 3-day funding rate
                'adjusted_funding': adjusted_funding,    # Cost-adjusted annualized funding rate
                'current_funding': current_funding,  # Raw current funding rate
                'annualized_current_funding': annualized_current_funding,  # Annualized current funding rate
                'avg_volume_5d_usdt': validated_volume,
                'price': Decimal(str(validated_price)),
                'base': market.get('base', ''),
                'quote': market.get('quote', ''),
            }

        except Exception as e:
            logger.debug(f"⚠️ Error processing {symbol}: {e}")
            return None
