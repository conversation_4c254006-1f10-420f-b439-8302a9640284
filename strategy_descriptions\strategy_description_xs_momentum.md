Strategy Name: Cross-Sectional Momentum Strategy
Strategy Type: Momentum

# 1. STRATEGY OVERVIEW
Core Principle: Exploit relative strength across crypto assets by taking long positions in outperforming assets and short positions in underperforming assets
Expected Edge: Crypto markets exhibit persistent trends that can be captured through cross-sectional ranking
Time Horizon: Medium to long-term (daily rebalancing)
Market Conditions: Performs best in strong directional markets; struggles in range-bound or highly volatile conditions

# 2. UNIVERSE SELECTION
Selection Criteria:
- Market Cap Range: Top 50 by market cap (using coingecko api)
- Contracts: Perpetual futures only (USDT pairs)
- Volume Threshold: $3M minimum daily volume (30-day average)
- Volatility Filters: Annualized weighted volatility above 5% (to filter out stablecoins)
- Historical Data: Minimum 150 days of close price history required
- Exclusions: Stablecoins, wrapped coins
- Final universe: Up to 50 coins that pass all filters

Implementation Details:
- Market Cap Data Source: CoinGecko API for accurate market cap rankings
- Price/Volume Data Source: Primary exchange API
- Processing: Batch processing with API rate limiting (20 symbols per batch)
- Refresh Frequency: Weekly universe refresh on Sunday at 00:00 UTC
- Fallback Mechanism: Use previous universe if refresh fails (max 14 days old)

# 3. FEATURE ENGINEERING
Primary Features:
1. **Rolling Beta Calculation**
   - Calculate 60-day rolling beta against BTC: `beta = covariance(coin_returns, btc_returns) / variance(btc_returns)`
   - Fallback: Default beta of 1.0 for calculation failures
   - Sanity checks: Cap extreme values at ±10

2. **Beta-Adjusted Log Prices**
   - Formula: `beta_adjusted_log_price = coin_log_price - (beta × btc_log_price)`
   - Purpose: Remove market-wide movements to isolate asset-specific trends

3. **Weighted Volatility**
   - Formula: `weighted_volatility = (0.3 × vol_60d) + (0.5 × vol_30d) + (0.2 × vol_10d)`
   - Annualized volatility calculation (365-day basis for crypto 24/7 trading)
   - Used for signal normalization

4. **Momentum Signals from Multiple EMA Timeframes**
   - EMA pairs: 2/8, 4/16, 8/32, 16/64, 32/128 periods
   - Signal formula for each timeframe: `(short_ema - long_ema) / weighted_volatility`
   - Equal weight combination: `trend_signal = sum(all_timeframe_signals) / 5`

Feature Validation:
- Data Quality Checks: Minimum 150 days of close price history required
- Error Handling: Skip symbols with calculation failures, use fallback values
- Batch Processing: Process 10 symbols at a time to manage API limits

# 4. POSITION SELECTION
Selection Process:
- **Method**: Cross-sectional ranking by combined trend signal
- **Long Positions**: Top 5 coins with highest trend signals (regardless of sign)
- **Short Positions**: Top 5 coins with lowest trend signals (bottom 5 in cross-sectional ranking)
- **Key Feature**: Allows short positions even when trend signals are non-negative, as long as they rank as the lowest trend signals
- **Ranking**: Pure cross-sectional ranking - relative performance matters, not absolute signal values
- **Implementation**: Sort all symbols by trend signal, select top 5 for longs and bottom 5 for shorts

# 5. POSITION SIZING
Sizing Methodology:
1. **Sigmoid Weighting**: Apply `tanh(2*trend_signal)` to convert signals to base weights
2. **Volatility Targeting**: Adjust weights by `target_volatility / asset_volatility` (target = 30%)
3. **Weight Normalization**: Normalize all weights to sum to 1
4. **Beta Projection** (Disabled): Beta projection disabled since beta adjustment is done in feature calculation

Risk Controls:
- **Leverage Limits**: No leverage limits (volatility targeting provides control)
- **Position Limits**: No maximum position size restrictions
- **Buffer Zones**: 5% tolerance around target positions to prevent over-trading
- **Beta Neutrality**: Optional portfolio beta optimization with max 20% weight change

Dynamic Adjustments:
- **Rebalancing**: Daily at 00:00 UTC
- **Universe Refresh**: Weekly on Sundays
- **No intraday adjustments**

# 6. EXIT RULES
Profit Taking:
- None (rely on momentum signals for position management)

Stop Losses:
- None (rely on cross-sectional ranking for risk management)

Rebalancing:
- **Schedule**: Daily rebalancing at 00:00 UTC
- **Execution**: Use existing randomized execution engine with buffer zones
- **Position Closure**: Close all positions not in target list (no dust protection)

# 7. CONFIGURATION PARAMETERS
Key Implementation Parameters:
- `top_market_cap_count`: 50 (universe size)
- `max_positions_per_leg`: 5 (positions per side)
- `target_volatility`: 0.30 (30% target portfolio volatility)
- `min_volatility_threshold`: 0.05 (5% minimum to exclude stablecoins)
- `beta_calculation_days`: 60 (rolling beta window)
- `ema_timeframes`: [[2,8], [4,16], [8,32], [16,64], [32,128]]
- `enable_beta_projection`: false (disabled since beta adjustment done in features)
- `buffer_zone_tolerance_percentage`: 5.0 (position tolerance)