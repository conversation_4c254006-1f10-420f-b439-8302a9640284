Strategy Name: Daily Trend Strategy
Strategy Type: Trend Following

# 1. STRATEGY OVERVIEW
Core Principle: Capture directional price movements daily timeframes
Expected Edge: Crypto markets exhibit persistent trends
Time Horizon: Medium to long-term
Market Conditions: Performs best in strong directional markets; struggles in range-bound or highly volatile conditions

# 2. UNIVERSE SELECTION
Selection Criteria:
- Market Cap Range: Top 50 by market cap
- Contracts: Perpetual futures only
- Volume Threshold: $3M minimum daily volume (5-day average)
- Volatility Filters: annualized weighted volatility above 5% (to filter out stablecoins)
- Exclusions: Stablecoins

Implementation Details:
- Data Source: Primary exchange API with fallback to aggregated data
- Refresh Frequency: Weekly universe refresh on Sunday at 00:00 UTC
- Fallback Mechanism: If primary data unavailable, use previous universe

# 3. FEATURE ENGINEERING
Primary Features:
- Calculate equal weight (mean) log prices of top 50 coins. market_log_prices = log_prices[available_coins].mean(axis=1)
- Calculate relative log prices for each coin by subtracting. relative_log_price = coin_log_price - market_log_price
- Calculate equal weight combined signal from 2/8, 4/16, 8/32, 16/64, and 32/128 EMA of relative_log_price. The feature formula for each timeframe should be: (n1 - n2) / weighted_volatility. Equal weight each timeframe's score and sum them up to get the final trend signal feature.


Feature Validation:
- Data Quality Checks: Require minimum 150 days of price history

# 4. POSITION SELECTION
Selection Process:
- This is a time series strategy, not a cross sectional strategy, so all positions are independent. All coins will have its own positions.

# 5. POSITION SIZING
Sizing Methodology:
- Pass the trend signal to a sigmoid function y = tanh(2x) to get target weights, where x = trend signal.
- Adjust the weight of each coin by applying volatility targeting to each coin by its weighted volatility. target_volatility = 0.30. adjusted_weight = base_weight × (target_volatility / asset_volatility)
- Normalize the weights to sum to 1. normalized_weights = adjusted_weight / adjusted_weight.sum()

Risk Controls:
- Leverage Limits: No leverage limits

Dynamic Adjustments:
- No dynamic adjustments


# 6. EXIT RULES
Profit Taking:
- None

Stop Losses:
- None

Rebalancing:
- Schedule: Daily rebalancing at 00:00 UTC
- Use existing execution engine for rebalancing, no-trade buffer zones, etc.

# 7. CONFIGURATION PARAMETERS