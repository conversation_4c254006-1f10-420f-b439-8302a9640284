Strategy Name: Daily Trend Strategy
Strategy Type: Trend Following

# 1. STRATEGY OVERVIEW
Core Principle: Capture directional price movements daily timeframes
Expected Edge: Crypto markets exhibit persistent trends
Time Horizon: Medium to long-term
Market Conditions: Performs best in strong directional markets; struggles in range-bound or highly volatile conditions

# 2. UNIVERSE SELECTION
Selection Criteria:
- Market Cap Range: Top 50 by market cap (using coingecko api)
- Contracts: Perpetual futures only (USDT pairs)
- Volume Threshold: $3M minimum daily volume (30-day average)
- Volatility Filters: Annualized weighted volatility above 5% (to filter out stablecoins)
- Historical Data: Minimum 150 days of close price history required
- Exclusions: Stablecoins, wrapped coins
- Final universe: Up to 50 coins that pass all filters

Implementation Details:
- Data Source: Primary exchange API
- Processing: Batch processing with API rate limiting (20 symbols per batch)
- Refresh Frequency: Weekly universe refresh on Sunday at 00:00 UTC
- Fallback Mechanism: Use previous universe if refresh fails (max 14 days old)

# 3. FEATURE ENGINEERING
Primary Features:
1. **Weighted Volatility**
   - Formula: `weighted_volatility = (0.3 × vol_60d) + (0.5 × vol_30d) + (0.2 × vol_10d)`
   - Annualized volatility calculation (365-day basis for crypto 24/7 trading)
   - Used for signal normalization

2. **Momentum Signals from Multiple EMA Timeframes**
   - EMA pairs: 2/8, 4/16, 8/32, 16/64, 32/128 periods
   - Signal formula for each timeframe: `(short_ema - long_ema) / weighted_volatility`
   - Equal weight combination: `trend_signal = sum(all_timeframe_signals) / 5`

Feature Validation:
- Data Quality Checks: Minimum 150 days of close price history required
- Error Handling: Skip symbols with calculation failures, use fallback values
- Batch Processing: Process 10 symbols at a time to manage API limits


# 4. POSITION SELECTION
Selection Process:
- This is a time series strategy, not a cross sectional strategy.All coins will have its own positions.

# 5. POSITION SIZING
Sizing Methodology:
1. **Sigmoid Weighting**: Apply `tanh(2*trend_signal)` to each coin's trend signal to convert signals to base weights
2. **Volatility Targeting**: Adjust each coin'sweights by `target_volatility / asset_volatility` (target = 30%)
3. **Equal Capital Allocation**: Adjust each coin's weights by multiplying 1/N for equal capital allocation in portfolio.
4. **Beta Projection** (Disabled): Beta projection disabled since beta adjustment is done in feature calculation

Risk Controls:
- **Leverage Limits**: No leverage limits (volatility targeting provides control)
- **Position Limits**: No maximum position size restrictions
- **Buffer Zones**: 5% tolerance around target positions to prevent over-trading
- **Beta Neutrality**: Optional portfolio beta optimization with max 20% weight change

Dynamic Adjustments:
- **Rebalancing**: Daily at 00:00 UTC
- **Universe Refresh**: Weekly on Sundays
- **No intraday adjustments**


# 6. EXIT RULES
Profit Taking:
- None

Stop Losses:
- None

Rebalancing:
- **Schedule**: Daily rebalancing at 00:00 UTC
- **Execution**: Use existing randomized execution engine with buffer zones
- **Position Closure**: Close all positions not in target list (no dust protection)

# 7. CONFIGURATION PARAMETERS