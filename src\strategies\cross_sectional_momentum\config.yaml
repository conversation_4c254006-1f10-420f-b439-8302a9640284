# Cross-Sectional Momentum Strategy Configuration
# Strategy-specific parameters for momentum-based trading

# ============================================================================
# UNIVERSE SELECTION PARAMETERS
# ============================================================================

# Market cap and volume filters
top_market_cap_count: 50           # Top 50 coins by market cap
min_daily_volume_usd: 3000000      # Minimum $3M daily volume (30-day average)
exclude_new_listings_days: 60      # Exclude coins listed within this many days
min_historical_data_days: 150      # Minimum 150 days of close price history required

# Volatility filters
min_volatility_threshold: 0.05     # Minimum volatility to exclude stablecoins (5%)

# Exclusions
exclude_stablecoins: true          # Exclude stablecoins
exclude_wrapped_coins: true        # Exclude wrapped coins

# ============================================================================
# FEATURE ENGINEERING PARAMETERS
# ============================================================================

# Beta calculation
beta_calculation_days: 60          # Number of days for rolling beta calculation
market_index_symbol: "BTCUSDT"     # Market index symbol for beta calculation

# Volatility calculation weights
volatility_weights:
  vol_60d: 0.3                     # 30% weight for 60-day volatility
  vol_30d: 0.5                     # 50% weight for 30-day volatility
  vol_10d: 0.2                     # 20% weight for 10-day volatility

# EMA momentum timeframes for trend signal calculation
ema_timeframes:
  - [2, 8]                         # 2/8 EMA crossover
  - [4, 16]                        # 4/16 EMA crossover
  - [8, 32]                        # 8/32 EMA crossover
  - [16, 64]                       # 16/64 EMA crossover
  - [32, 128]                      # 32/128 EMA crossover

# Equal weight combination of all timeframes
equal_weight_combination: true     # Use equal weights for combining timeframe signals

# ============================================================================
# POSITION SELECTION PARAMETERS
# ============================================================================

# Cross-sectional selection
max_positions_per_leg: 5           # Top 5 positions per leg (long/short)

# Position ranking criteria
# Primary: Trend signal (highest for longs, lowest for shorts)

# ============================================================================
# POSITION SIZING PARAMETERS
# ============================================================================

# Sigmoid weighting function
use_sigmoid_weighting: true        # Apply tanh(x) to trend signals for weights
sigmoid_function: "tanh"           # Sigmoid function type (tanh)

# Volatility targeting
target_volatility: 0.30            # Target portfolio volatility (30%)

# Risk management
enable_beta_projection: true       # Enable beta-neutral portfolio construction
beta_neutrality_tolerance: 0.05    # Maximum allowed portfolio beta deviation (±5%)
beta_optimization_max_weight_change: 0.20  # Maximum weight change during beta optimization (20%)

# ============================================================================
# STRATEGY-SPECIFIC RISK CONTROLS
# ============================================================================

# Position limits
max_leverage: null                 # No leverage limits
max_position_size_usd: null        # No maximum position size

# Buffer zones for position management
buffer_zone_tolerance_percentage: 5.0    # Buffer zone around target position (% of individual coin's USD position)

# Position closure policy
close_all_non_target_positions: true     # Close all positions not in target list

# ============================================================================
# EXECUTION PREFERENCES
# ============================================================================

# Rebalancing frequency
rebalancing_frequency: "daily"     # Daily rebalancing at 00:00 UTC
rebalancing_time_utc: "00:00"      # UTC time for rebalancing

# Order execution preferences
preferred_execution_style: "conservative"  # conservative, balanced, aggressive
max_execution_time_minutes: 30     # Maximum time to spend executing this strategy's positions

# ============================================================================
# PERFORMANCE AND MONITORING
# ============================================================================

# Strategy-specific monitoring
enable_momentum_alerts: true       # Alert on extreme momentum changes
momentum_alert_threshold: 2.0      # Alert threshold for momentum signal changes (2 std devs)

# Performance tracking
track_individual_position_pnl: true  # Track P&L for each position
enable_strategy_attribution: true   # Enable detailed strategy attribution

# Logging preferences
log_position_selection_details: true  # Log detailed position selection reasoning
log_feature_calculations: true      # Log feature calculation details
log_beta_calculations: true         # Log beta calculation details

# ============================================================================
# FALLBACK AND DEFAULT VALUES
# ============================================================================

# Default values for missing data
default_volatility: 0.20           # Default volatility fallback (20%)
default_beta: 1.0                   # Default beta fallback (1.0 = market beta)
default_trend_signal: 0.0          # Default trend signal fallback

# Error handling
skip_problematic_symbols: true     # Skip symbols that fail validation
max_symbol_failures_per_execution: 5  # Maximum symbol failures before stopping

# Data validation
validate_price_data: true          # Validate price data quality
validate_volume_data: true         # Validate volume data quality
validate_beta_data: true           # Validate beta calculation data quality

# ============================================================================
# UNIVERSE REFRESH SETTINGS
# ============================================================================

# Universe refresh schedule
universe_refresh_frequency: "weekly"  # Weekly universe refresh
universe_refresh_day: "sunday"     # Day of week for universe refresh
universe_refresh_time_utc: "00:00" # UTC time for universe refresh

# Fallback mechanism
use_previous_universe_on_failure: true  # Use previous universe if refresh fails
max_universe_age_days: 14           # Maximum age of universe before forced refresh
