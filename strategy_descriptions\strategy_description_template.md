Strategy Name: [STRATEGY_NAME]
Strategy Type: [STRATEGY_TYPE]

# 1. STRATEGY OVERVIEW
Core Principle: [Brief explanation of the strategy's core principle]
Expected Edge: [Source of alpha/edge in the market]
Time Horizon: [Short-term, medium-term, long-term]
Market Conditions: [When strategy is expected to perform best/worst]

# 2. UNIVERSE SELECTION
Selection Criteria:
- Market Cap Range: [e.g., Top 50 by market cap]
- Volume Threshold: [Minimum daily volume in USD]
- Volatility Filters: [Any volatility-based filters]
- Exclusions: [e.g., Stablecoins, new listings]
- Additional Filters: [Any other filters specific to this strategy]

Implementation Details:
- Data Source: [Which API/data source to use]
- Refresh Frequency: [How often to refresh universe]
- Fallback Mechanism: [What to do if primary data unavailable]

# 3. FEATURE ENGINEERING
Primary Features:
- Feature 1: [Name, calculation method, lookback period]
- Feature 2: [Name, calculation method, lookback period]
- Feature 3: [Name, calculation method, lookback period]

Feature Validation:
- Outlier Handling: [How to handle extreme values]
- Data Quality Checks: [Minimum data requirements]
- Normalization: [Any normalization or standardization]

Feature Combination:
- Weighting Scheme: [How to combine multiple features]
- Interaction Terms: [Any feature interactions to consider]
- Feature Decay: [Time decay for older signals]

# 4. POSITION SELECTION
Selection Process:
- Signal Threshold: [Minimum signal strength for consideration]
- Ranking System: [How candidates are ranked]
- Long/Short Classification: [Criteria for long vs short positions]

Selection Constraints:
- Minimum Positions: [Minimum positions per leg]
- Maximum Positions: [Maximum positions per leg]
- Balance Requirements: [e.g., Equal long/short exposure]

Entry Conditions:
- Timing Rules: [Any time-based entry conditions]
- Confirmation Signals: [Secondary signals required]
- Entry Execution: [Market/limit orders, price improvement]

# 5. POSITION SIZING
Sizing Methodology:
- Base Approach: [e.g., Equal weight, volatility targeting, etc.]
- Target Volatility: [If using volatility targeting]
- Position Limits: [Maximum allocation per position]

Risk Controls:
- Beta Neutrality: [Yes/No, tolerance level]
- Sector Neutrality: [Yes/No, implementation]
- Leverage Limits: [Min/Max leverage]

Dynamic Adjustments:
- Size Scaling: [Conditions for scaling positions]
- Correlation Adjustments: [How to adjust for correlated positions]
- Volatility Regime Adaptation: [Adjustments based on market regime]

# 6. EXIT RULES
Profit Taking:
- Target Levels: [Profit targets if applicable]
- Scaling Out: [Rules for partial exits]
- Time-Based Exits: [Maximum holding periods]

Stop Losses:
- Fixed Stops: [Fixed percentage/dollar stops]
- Volatility-Based Stops: [ATR-based stops]
- Signal Reversal: [Exit when signal reverses]

Rebalancing:
- Schedule: [Regular rebalancing frequency]
- Threshold-Based: [Deviation thresholds for rebalancing]
- Buffer Zones: [Tolerance before adjusting positions]

# 7. CONFIGURATION PARAMETERS
Universe Parameters:
- min_daily_volume_usd: [Value]
- exclude_new_listings_days: [Value]
- [Other universe parameters]

Feature Calculation Parameters:
- [feature_1]_lookback_days: [Value]
- [feature_2]_lookback_days: [Value]
- [Other feature parameters]

Position Selection Parameters:
- signal_threshold: [Value]
- min_positions_per_leg: [Value]
- [Other selection parameters]

Position Sizing Parameters:
- target_volatility: [Value]
- max_position_capital_pct: [Value]
- enable_beta_projection: [True/False]
- [Other sizing parameters]

Risk Management Parameters:
- buffer_zone_tolerance_percentage: [Value]
- min_close_threshold_usd: [Value]
- [Other risk parameters]

# 8. IMPLEMENTATION DETAILS
Required Methods:
- get_universe(): [Specific implementation details]
- calculate_features(): [Specific implementation details]
- select_positions(): [Specific implementation details]
- size_positions(): [Specific implementation details]

Dependencies:
- External Libraries: [Any non-standard libraries needed]
- Internal Components: [Dependencies on other system components]
- Data Requirements: [Specific data needs]

Error Handling:
- Critical Failures: [How to handle critical errors]
- Data Gaps: [Handling missing data]
- Fallback Mechanisms: [Alternative approaches if primary fails]

# 9. PERFORMANCE METRICS
Key Metrics:
- Expected Sharpe: [Target Sharpe ratio]
- Expected Turnover: [Estimated portfolio turnover]
- Correlation: [Expected correlation with other strategies]

Monitoring Indicators:
- Health Checks: [Indicators that strategy is functioning properly]
- Warning Signs: [Indicators that strategy may be breaking down]
- Adaptation Triggers: [Conditions that require parameter adjustments]

# 10. TESTING APPROACH
Historical Testing:
- Test Period: [Date ranges for backtesting]
- Metrics: [Key metrics to evaluate]
- Robustness Checks: [Parameter sensitivity, etc.]

Forward Testing:
- Paper Trading: [Approach to paper trading]
- Scaling Plan: [How to scale from paper to live]
- Monitoring Framework: [What to monitor during live trading]