# Enhanced Error Handling System

This document describes the comprehensive error handling system implemented for API rate limits and network issues.

## Overview

The enhanced error handling system provides sophisticated error classification, retry logic, circuit breaker patterns, and recovery mechanisms to ensure robust operation in the face of API rate limits and network issues.

## Key Components

### 1. Error Classification (`ErrorClassifier`)

Automatically classifies errors into specific types for appropriate handling:

- **Rate Limit Errors**: API rate limit exceeded, throttling
- **Network Errors**: Connection timeouts, DNS issues, SSL problems
- **Exchange Maintenance**: Scheduled maintenance, service unavailable
- **Balance Errors**: Insufficient funds, margin issues
- **Order Errors**: Invalid order parameters, market closed
- **Generic API Errors**: Other API-related issues

### 2. Exponential Backoff (`ExponentialBackoff`)

Implements intelligent retry delays with:
- Configurable base delay and multiplier
- Maximum delay caps
- Optional jitter to prevent thundering herd
- Adaptive delays based on error type

### 3. Circuit Breaker (`CircuitBreaker`)

Prevents cascading failures with:
- **Closed State**: Normal operation
- **Open State**: Blocks requests after failure threshold
- **Half-Open State**: Limited testing after timeout
- Adaptive thresholds based on recent performance

### 4. Error Recovery Manager (`ErrorRecoveryManager`)

Orchestrates comprehensive retry logic:
- Error-specific retry strategies
- Circuit breaker integration
- Performance tracking
- Configurable retry limits per error type

### 5. Enhanced Retry Decorator (`@enhanced_retry`)

Provides easy-to-use decorator for functions:
- Automatic error classification
- Intelligent retry logic
- Exchange and operation context
- Seamless integration

## Configuration

### Basic Configuration

```yaml
error_handling:
  # Retry configuration
  max_retries: 3                 # Default max retries
  rate_limit_max_retries: 5      # Max retries for rate limits
  network_max_retries: 3         # Max retries for network errors
  
  # Exponential backoff
  retry_base_delay: 1.0          # Base delay in seconds
  retry_max_delay: 300.0         # Maximum delay (5 minutes)
  retry_multiplier: 2.0          # Backoff multiplier
  retry_jitter: true             # Add random jitter
  
  # Network timeouts
  connect_timeout: 30            # Connection timeout
  read_timeout: 60               # Read timeout
  total_timeout: 120             # Total request timeout

circuit_breaker:
  failure_threshold: 5           # Failures before opening
  timeout_seconds: 60            # Recovery timeout
  half_open_max_calls: 3         # Test calls in half-open state
```

## Usage Examples

### 1. Using the Decorator

```python
from utils.error_handler import enhanced_retry

@enhanced_retry(max_retries=3, exchange="bybit", operation="fetch_ticker")
async def fetch_ticker_data(symbol: str):
    return await exchange.fetch_ticker(symbol)
```

### 2. Using Error Recovery Manager

```python
from utils.error_handler import ErrorRecoveryManager

manager = ErrorRecoveryManager(config)

result = await manager.execute_with_retry(
    api_function,
    exchange="bybit",
    symbol="BTCUSDT",
    operation="place_order"
)
```

### 3. Manual Error Classification

```python
from utils.error_handler import ErrorClassifier

try:
    await api_call()
except Exception as e:
    classified_error = ErrorClassifier.classify_error(e, "bybit", "BTCUSDT")
    if isinstance(classified_error, RateLimitError):
        # Handle rate limit specifically
        await asyncio.sleep(classified_error.retry_after or 60)
```

## Error Types and Handling

### Rate Limit Errors
- **Detection**: "rate limit", "too many requests", "throttled"
- **Strategy**: Longer delays, respect retry-after headers
- **Max Retries**: 5 (configurable)

### Network Errors
- **Detection**: "timeout", "connection", "network", "dns"
- **Strategy**: Standard exponential backoff
- **Max Retries**: 3 (configurable)

### Exchange Maintenance
- **Detection**: "maintenance", "unavailable", "offline"
- **Strategy**: Extended delays, fewer retries
- **Max Retries**: 2 (configurable)

### Non-Retryable Errors
- Insufficient balance
- Invalid order parameters
- Market closed
- Authentication failures

## Integration Points

### 1. Data Fetcher
- Enhanced `_rate_limited_api_call` method
- Automatic error classification and retry
- Circuit breaker integration

### 2. Randomized Executor
- Enhanced order placement methods
- Rate limit aware execution
- Comprehensive error tracking

### 3. Exchange Implementations
- Decorated critical methods
- Exchange-specific error handling
- Consistent error reporting

## Monitoring and Debugging

### Error Statistics
```python
# Get error statistics
stats = error_manager.get_error_stats()
print(f"Error counts: {stats['error_counts']}")
print(f"Circuit breaker status: {stats['circuit_breaker']}")
```

### Circuit Breaker Status
```python
# Check circuit breaker status
status = circuit_breaker.get_status()
print(f"State: {status['state']}")
print(f"Failure count: {status['failure_count']}")
print(f"Can execute: {status['can_execute']}")
```

### Logging
The system provides comprehensive logging at different levels:
- **INFO**: Successful operations, circuit state changes
- **WARNING**: Retries, rate limits, recoverable errors
- **ERROR**: Failed operations after all retries
- **DEBUG**: Detailed retry attempts, timing information

## Best Practices

### 1. Configuration Tuning
- Start with conservative retry limits
- Adjust based on exchange-specific behavior
- Monitor error rates and adjust thresholds

### 2. Error Handling Strategy
- Use decorators for simple cases
- Use ErrorRecoveryManager for complex scenarios
- Always handle non-retryable errors gracefully

### 3. Performance Considerations
- Circuit breakers prevent resource waste
- Jitter prevents thundering herd effects
- Adaptive thresholds improve over time

### 4. Testing
- Test with simulated errors
- Verify retry behavior
- Check circuit breaker functionality

## Troubleshooting

### Common Issues

1. **Too Many Retries**
   - Reduce max_retries configuration
   - Check if errors are actually retryable
   - Verify network connectivity

2. **Circuit Breaker Always Open**
   - Check failure_threshold setting
   - Verify underlying service health
   - Review error classification

3. **Slow Performance**
   - Reduce retry delays for testing
   - Check if jitter is appropriate
   - Monitor actual error rates

### Debug Mode
Enable debug logging to see detailed retry behavior:
```yaml
debug_mode: true
```

This will log:
- Each retry attempt
- Delay calculations
- Error classifications
- Circuit breaker state changes

## Future Enhancements

Potential improvements for the error handling system:
- Machine learning-based error prediction
- Dynamic retry strategy adjustment
- Cross-exchange error correlation
- Advanced monitoring dashboards
- Automated alerting on error patterns
