# Cross-Sectional Momentum Strategy

## Table of Contents
- [Overview](#overview)
- [Universe Selection](#universe-selection)
- [Feature Engineering](#feature-engineering)
- [Position Selection](#position-selection)
- [Position Sizing](#position-sizing)
- [Risk Management](#risk-management)
- [Configuration](#configuration)
- [Performance Monitoring](#performance-monitoring)

## Overview

The Cross-Sectional Momentum Strategy exploits relative strength across crypto assets by taking long positions in outperforming assets and short positions in underperforming assets. The strategy uses beta-adjusted momentum signals derived from multiple EMA timeframes to identify trending assets.

**Core Principle**: Crypto markets exhibit persistent trends that can be captured through cross-sectional ranking of momentum signals.

**Expected Edge**: Relative momentum persists across crypto assets, allowing profitable long/short positioning.

**Time Horizon**: Medium to long-term (daily rebalancing)

**Market Conditions**: Performs best in strong directional markets; may struggle in range-bound or highly volatile conditions.

## Universe Selection

### Selection Criteria
- **Market Cap Range**: Top 50 coins by market cap (using volume as proxy)
- **Contract Type**: Perpetual futures only (USDT pairs)
- **Volume Threshold**: $3M minimum daily volume (30-day average)
- **Volatility Filter**: Annualized weighted volatility above 5% (excludes stablecoins)
- **Historical Data**: Minimum 150 days of close price history
- **Exclusions**: Stablecoins, wrapped coins

### Implementation Details
- **Data Source**: Primary exchange API
- **Refresh Frequency**: Weekly universe refresh on Sunday at 00:00 UTC
- **Fallback Mechanism**: Use previous universe if refresh fails
- **Processing**: Batch processing with API rate limiting

## Feature Engineering

### Primary Features

1. **Rolling Beta Calculation**
   - Calculate 60-day rolling beta against BTC
   - Formula: `beta = covariance(coin_returns, btc_returns) / variance(btc_returns)`
   - Fallback: Default beta of 1.0 for calculation failures

2. **Beta-Adjusted Log Prices**
   - Formula: `beta_adjusted_log_price = coin_log_price - (beta × btc_log_price)`
   - Purpose: Remove market-wide movements to isolate asset-specific trends

3. **Weighted Volatility**
   - Formula: `weighted_vol = 0.3 × vol_60d + 0.5 × vol_30d + 0.2 × vol_10d`
   - Annualized volatility calculation
   - Used for signal normalization

4. **Momentum Signals**
   - Multiple EMA timeframes: 2/8, 4/16, 8/32, 16/64, 32/128
   - Signal formula: `(short_ema - long_ema) / weighted_volatility`
   - Equal weight combination of all timeframes

### Feature Validation
- **Data Quality Checks**: Minimum 150 days of price history
- **Error Handling**: Skip symbols with calculation failures
- **Fallback Values**: Default values for missing data

## Position Selection

### Selection Process
- **Method**: Cross-sectional ranking by combined trend signal
- **Long Positions**: Top 5 coins with highest trend signals
- **Short Positions**: Top 5 coins with lowest trend signals
- **Ranking**: Single-tier ranking by trend signal strength

### Selection Logic
```python
# Sort all symbols by trend signal (descending)
sorted_symbols = sorted(symbols, key=lambda x: x['trend_signal'], reverse=True)

# Select top 5 for longs (highest signals)
long_candidates = sorted_symbols[:5]

# Select bottom 5 for shorts (lowest signals)  
short_candidates = sorted_symbols[-5:]
```

## Position Sizing

### Sizing Methodology

1. **Sigmoid Weighting**
   - Apply `tanh(trend_signal)` to convert signals to weights
   - Take absolute value (direction handled by long/short designation)

2. **Volatility Targeting**
   - Target portfolio volatility: 30%
   - Adjustment: `weight × (target_vol / asset_vol)`
   - Individual asset volatility targeting

3. **Weight Normalization**
   - Normalize all weights to sum to 1
   - Equal weight fallback for edge cases

4. **Beta Projection** (Optional)
   - Optimize for portfolio beta neutrality
   - Target beta: 0.0 ± 5% tolerance
   - Maximum weight change: 20%

### Risk Controls
- **Leverage Limits**: No leverage limits (volatility targeting provides control)
- **Position Limits**: No maximum position size restrictions
- **Buffer Zones**: 5% tolerance around target positions

## Risk Management

### Portfolio-Level Controls
- **Volatility Targeting**: 30% target portfolio volatility
- **Beta Neutrality**: Optional beta-neutral portfolio construction
- **Position Buffers**: 5% tolerance zones to prevent over-trading

### Dynamic Adjustments
- **Rebalancing**: Daily at 00:00 UTC
- **Universe Refresh**: Weekly on Sundays
- **No intraday adjustments**

### Exit Rules
- **Profit Taking**: None (rely on momentum signals)
- **Stop Losses**: None (rely on cross-sectional ranking)
- **Position Closure**: Close all non-target positions

## Configuration

### Key Parameters
```yaml
# Universe Selection
top_market_cap_count: 50
min_daily_volume_usd: 3000000
min_volatility_threshold: 0.05

# Feature Engineering  
beta_calculation_days: 60
ema_timeframes: [[2,8], [4,16], [8,32], [16,64], [32,128]]

# Position Selection
max_positions_per_leg: 5

# Position Sizing
target_volatility: 0.30
enable_beta_projection: true
```

### Customization Options
- EMA timeframes can be modified
- Volatility weights are configurable
- Beta projection can be disabled
- Universe size is adjustable

## Performance Monitoring

### Strategy-Specific Metrics
- **Trend Signal Distribution**: Monitor signal strength across universe
- **Beta Neutrality**: Track portfolio beta deviation
- **Volatility Targeting**: Monitor realized vs target volatility
- **Position Turnover**: Track rebalancing frequency

### Alerts and Monitoring
- **Momentum Alerts**: Extreme momentum signal changes (2 std devs)
- **Beta Alerts**: Portfolio beta deviation beyond tolerance
- **Universe Alerts**: Failed universe refresh or insufficient symbols

### Logging and Debugging
- **Feature Calculations**: Detailed logging of beta, volatility, and momentum calculations
- **Position Selection**: Log ranking and selection reasoning
- **Position Sizing**: Track weight adjustments through sizing pipeline

## Implementation Notes

### Data Requirements
- **OHLCV Data**: Daily candles for 150+ days
- **Volume Data**: 30-day average volume for filtering
- **BTC Data**: Required for beta calculations

### Performance Considerations
- **Batch Processing**: Process symbols in batches to manage API limits
- **Caching**: Leverage shared data cache for efficiency
- **Parallel Execution**: Concurrent feature calculation where possible

### Error Handling
- **Graceful Degradation**: Skip problematic symbols
- **Fallback Values**: Default values for calculation failures
- **Validation**: Comprehensive data quality checks
