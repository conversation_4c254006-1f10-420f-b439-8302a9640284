# Cross-Sectional Momentum Strategy

## Table of Contents
- [Overview](#overview)
- [Universe Selection](#universe-selection)
- [Feature Engineering](#feature-engineering)
- [Position Selection](#position-selection)
- [Position Sizing](#position-sizing)
- [Risk Management](#risk-management)
- [Configuration](#configuration)
- [Performance Monitoring](#performance-monitoring)

## Overview

The Cross-Sectional Momentum Strategy exploits relative strength across crypto assets by taking long positions in outperforming assets and short positions in underperforming assets. The strategy uses beta-adjusted momentum signals derived from multiple EMA timeframes to identify trending assets.

**Core Principle**: Crypto markets exhibit persistent trends that can be captured through cross-sectional ranking of momentum signals.

**Expected Edge**: Relative momentum persists across crypto assets, allowing profitable long/short positioning.

**Time Horizon**: Medium to long-term (daily rebalancing)

**Market Conditions**: Performs best in strong directional markets; may struggle in range-bound or highly volatile conditions.

## Universe Selection

### Selection Criteria
- **Market Cap Range**: Top 50 coins by market cap (using CoinGecko API data)
- **Contract Type**: Perpetual futures only (USDT pairs)
- **Volume Threshold**: $3M minimum daily volume (30-day average)
- **Volatility Filter**: Annualized weighted volatility above 5% (excludes stablecoins)
- **Historical Data**: Minimum 150 days of close price history
- **Exclusions**: Stablecoins, wrapped coins

### Implementation Details
- **Market Cap Data Source**: CoinGecko API for accurate market cap rankings
- **Price/Volume Data Source**: Primary exchange API
- **Refresh Frequency**: Weekly universe refresh on Sunday at 00:00 UTC
- **Fallback Mechanism**: Use previous universe if refresh fails
- **Processing**: Batch processing with API rate limiting

## Feature Engineering

### Primary Features

1. **Rolling Beta Calculation**
   - Calculate 60-day rolling beta against BTC
   - Formula: `beta = covariance(coin_returns, btc_returns) / variance(btc_returns)`
   - Fallback: Default beta of 1.0 for calculation failures

2. **Beta-Adjusted Log Prices**
   - Formula: `beta_adjusted_log_price = coin_log_price - (beta × btc_log_price)`
   - Purpose: Remove market-wide movements to isolate asset-specific trends

3. **Weighted Volatility**
   - Formula: `weighted_vol = 0.3 × vol_60d + 0.5 × vol_30d + 0.2 × vol_10d`
   - Annualized volatility calculation
   - Used for signal normalization

4. **Momentum Signals**
   - Multiple EMA timeframes: 2/8, 4/16, 8/32, 16/64, 32/128
   - Signal formula: `(short_ema - long_ema) / weighted_volatility`
   - Equal weight combination of all timeframes

### Feature Validation
- **Data Quality Checks**: Minimum 150 days of price history
- **Error Handling**: Skip symbols with calculation failures
- **Fallback Values**: Default values for missing data

## Position Selection

### Selection Process
- **Method**: Cross-sectional ranking by combined trend signal
- **Long Positions**: Top 5 coins with highest trend signals (regardless of absolute value)
- **Short Positions**: Top 5 coins with lowest trend signals (bottom 5 in cross-sectional ranking)
- **Key Feature**: Allows short positions even when trend signals are non-negative, as long as they rank as the lowest trend signals
- **Ranking**: Pure relative ranking - what matters is relative performance, not absolute signal values

### Selection Logic
```python
# Sort all symbols by trend signal (descending)
sorted_symbols = sorted(symbols, key=lambda x: x['trend_signal'], reverse=True)

# Select top 5 for longs (highest signals)
long_candidates = sorted_symbols[:5]

# Select bottom 5 for shorts (lowest signals)
short_candidates = sorted_symbols[-5:]
```

### Cross-Sectional Logic Examples

**Example 1: All Positive Signals**
- Trend signals: [0.8, 0.6, 0.4, 0.2, 0.1, 0.05, 0.02, ...]
- Longs: Top 5 coins with signals [0.8, 0.6, 0.4, 0.2, 0.1]
- Shorts: Bottom 5 coins with signals [0.05, 0.02, ...] (still positive but lowest ranked)

**Example 2: All Negative Signals**
- Trend signals: [-0.1, -0.2, -0.4, -0.6, -0.8, -1.0, -1.2, ...]
- Longs: Top 5 coins with signals [-0.1, -0.2, -0.4, -0.6, -0.8] (least negative)
- Shorts: Bottom 5 coins with signals [-1.0, -1.2, ...] (most negative)

**Example 3: Mixed Signals**
- Trend signals: [0.5, 0.2, 0.1, -0.1, -0.3, -0.5, -0.8, ...]
- Longs: Top 5 coins with signals [0.5, 0.2, 0.1, -0.1, -0.3]
- Shorts: Bottom 5 coins with signals [-0.5, -0.8, ...]

### Linear Decay Weighting Example

**Position Selection and Weighting:**
```
Sorted by trend signal: [BTC: 0.8, ETH: 0.6, BNB: 0.4, ADA: 0.2, SOL: 0.1, ..., DOT: -0.5, LINK: -0.8]

Long Positions (Top 5):
1. BTC (0.8) → Weight: 0.15 (15%)
2. ETH (0.6) → Weight: 0.125 (12.5%)
3. BNB (0.4) → Weight: 0.1 (10%)
4. ADA (0.2) → Weight: 0.075 (7.5%)
5. SOL (0.1) → Weight: 0.05 (5%)
Total Long Weight: 0.5 (50%)

Short Positions (Bottom 5):
1. DOT (-0.5) → Weight: -0.15 (-15%)
2. LINK (-0.8) → Weight: -0.125 (-12.5%)
3. AVAX (-1.0) → Weight: -0.1 (-10%)
4. MATIC (-1.2) → Weight: -0.075 (-7.5%)
5. UNI (-1.5) → Weight: -0.05 (-5%)
Total Short Weight: -0.5 (-50%)
```

**After Volatility Targeting:**
Each weight is adjusted by `target_volatility / asset_volatility` to achieve 30% portfolio volatility.

## Position Sizing

### Sizing Methodology

1. **Linear Decay Weighting**
   - Apply predefined linear decay weights based on cross-sectional ranking
   - Long weights: [0.15, 0.125, 0.1, 0.075, 0.05] (top to bottom ranked, sums to 0.5)
   - Short weights: [-0.15, -0.125, -0.1, -0.075, -0.05] (top to bottom ranked, sums to -0.5)
   - Top-ranked positions get highest absolute weights

2. **Volatility Targeting**
   - Target portfolio volatility: 30%
   - Adjustment: `weight × (target_vol / asset_vol)`
   - Individual asset volatility targeting

3. **Contract Compliance and Price Validation**
   - Round position sizes to exchange-specific lot sizes and minimum order sizes
   - Validate price data using exchange-specific tick sizes
   - Ensure all orders meet contract specifications before execution

4. **Beta Projection** (Disabled by Default)
   - Beta projection is disabled since beta adjustment is already done in feature calculation
   - Can be optionally enabled if additional portfolio-level beta neutrality is desired
   - Target beta: 0.0 ± 5% tolerance when enabled

### Risk Controls
- **Leverage Limits**: No leverage limits (volatility targeting provides control)
- **Position Limits**: No maximum position size restrictions
- **Buffer Zones**: 5% tolerance around target positions

## Risk Management

### Portfolio-Level Controls
- **Volatility Targeting**: 30% target portfolio volatility
- **Beta Neutrality**: Disabled by default (beta adjustment done in feature calculation)
- **Position Buffers**: 5% tolerance zones to prevent over-trading

### Dynamic Adjustments
- **Rebalancing**: Daily at 00:00 UTC
- **Universe Refresh**: Weekly on Sundays
- **No intraday adjustments**

### Exit Rules
- **Profit Taking**: None (rely on momentum signals)
- **Stop Losses**: None (rely on cross-sectional ranking)
- **Position Closure**: Close all non-target positions

## Configuration

### Key Parameters
```yaml
# Universe Selection
top_market_cap_count: 50
min_daily_volume_usd: 3000000
min_volatility_threshold: 0.05

# Feature Engineering  
beta_calculation_days: 60
ema_timeframes: [[2,8], [4,16], [8,32], [16,64], [32,128]]

# Position Selection
max_positions_per_leg: 5

# Position Sizing
target_volatility: 0.30
use_linear_decay_weighting: true
long_weights: [0.15, 0.125, 0.1, 0.075, 0.05]
short_weights: [-0.15, -0.125, -0.1, -0.075, -0.05]
enable_beta_projection: false
```

### Customization Options
- **Linear Decay Weights**: Can be modified in config (long_weights and short_weights arrays)
- **EMA Timeframes**: Can be modified to use different momentum periods
- **Volatility Weights**: Configurable weights for volatility calculation (60d/30d/10d)
- **Beta Projection**: Can be enabled if additional portfolio-level neutrality is desired
- **Universe Size**: Adjustable (top_market_cap_count parameter)
- **Positions Per Leg**: Adjustable (max_positions_per_leg parameter)

## Performance Monitoring

### Strategy-Specific Metrics
- **Trend Signal Distribution**: Monitor signal strength across universe
- **Beta Neutrality**: Track portfolio beta deviation
- **Volatility Targeting**: Monitor realized vs target volatility
- **Position Turnover**: Track rebalancing frequency

### Alerts and Monitoring
- **Momentum Alerts**: Extreme momentum signal changes (2 std devs)
- **Beta Alerts**: Portfolio beta deviation beyond tolerance
- **Universe Alerts**: Failed universe refresh or insufficient symbols

### Logging and Debugging
- **Feature Calculations**: Detailed logging of beta, volatility, and momentum calculations
- **Position Selection**: Log ranking and selection reasoning
- **Position Sizing**: Track weight adjustments through sizing pipeline

## Implementation Notes

### Data Requirements
- **OHLCV Data**: Daily candles for 150+ days
- **Volume Data**: 30-day average volume for filtering
- **BTC Data**: Required for beta calculations

### Performance Considerations
- **Batch Processing**: Process symbols in batches to manage API limits
- **Caching**: Leverage shared data cache for efficiency
- **Parallel Execution**: Concurrent feature calculation where possible

### Error Handling
- **Graceful Degradation**: Skip problematic symbols
- **Fallback Values**: Default values for calculation failures
- **Validation**: Comprehensive data quality checks
